{"name": "mlife-web", "version": "0.1.0", "private": true, "description": "MLife", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write --ignore-unknown ."}, "dependencies": {"@coinbase/wallet-sdk": "^4.3.4", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@studio-freight/lenis": "^1.0.42", "@tanstack/react-query": "^5.81.5", "@wagmi/core": "^2.17.3", "@walletconnect/ethereum-provider": "^2.21.4", "@walletconnect/modal": "^2.7.0", "@walletconnect/sign-client": "2.10.5", "@web3modal/ethereum": "^2.7.1", "@web3modal/react": "^2.7.1", "@web3modal/wagmi": "^5.1.11", "ethers": "^6.14.4", "framer-motion": "^11.11.11", "lit": "^3.3.0", "mongoose": "^8.13.2", "next": "^15.2.1", "pdfjs-dist": "^3.11.174", "react": "19.0.0-rc-02c0e824-20241028", "react-dom": "19.0.0-rc-02c0e824-20241028", "react-hook-form": "^7.55.0", "react-icons": "^5.3.0", "react-pdf": "^9.2.1", "sonner": "^2.0.3", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "viem": "^2.31.6", "wagmi": "^2.15.6", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.0.2", "eslint-config-prettier": "^9.1.0", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.14", "typescript": "^5"}}