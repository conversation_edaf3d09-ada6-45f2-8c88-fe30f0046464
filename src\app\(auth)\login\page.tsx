'use client';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { FaRegEye, FaRegEyeSlash } from 'react-icons/fa';
import { FcGoogle } from 'react-icons/fc';
import { GoArrowLeft } from 'react-icons/go';
import { MdEmail } from 'react-icons/md';
import { toast } from 'sonner';

type FormData = {
    email: string;
    password: string;
};
function Login() {
    const {
        register,
        handleSubmit,
        reset,
        formState: { errors, isValid }
    } = useForm<FormData>({ mode: 'onChange' });
    const [showPassword, setShowPassword] = useState(false);
    const router = useRouter();
    const onSubmit = (data: FormData) => {
        console.log(data);
        toast.success('Login successful!');
        router.push('/dashboard');
        reset();
    };
    const loginWithGoogle = () => {
        // Implement Google login logic here
        toast.success('Login with Google successful!');
    };
    const loginWithEmail = () => {
        // Implement email login logic here
        toast.success('Login with email successful!');
    };
    return (
        <div className="mb-10">
            <div className="border-b">
                <header className="mx-auto flex max-w-screen-xl items-center justify-between px-4 py-5 md:px-0">
                    <Link
                        href="/"
                        className="flex items-center gap-1 text-[#666666] hover:text-[#000000]"
                    >
                        <GoArrowLeft />
                        Back
                    </Link>
                    <Link
                        href="/register"
                        className="flex items-center gap-2 text-[#0088FF] underline underline-offset-4 hover:text-blue-700"
                    >
                        Create an account
                    </Link>
                </header>
            </div>

            <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4">
                <div className="absolute hidden text-center text-xs text-[#333333] md:top-36 md:block">
                    <h1 className="text-4xl">Log in</h1>
                </div>
                <div className="flex w-full max-w-4xl flex-col items-center space-y-6 md:flex-row md:space-x-8 md:space-y-0">
                    {/* Left - Login Form */}
                    <div className="w-4/5 md:w-full md:flex-1">
                        <h2 className="mb-6 text-center text-2xl">Log in</h2>
                        <form
                            onSubmit={handleSubmit(onSubmit)}
                            className="space-y-6"
                        >
                            <div>
                                <label className="mb-1 block text-base font-medium text-[#666666]">
                                    Email address
                                </label>
                                <input
                                    type="email"
                                    {...register('email', {
                                        required: 'Email is required'
                                    })}
                                    className="h-12 w-full rounded-lg border border-[#666666] px-3 py-2 focus:border-black focus:outline-1"
                                />
                                {errors.email && (
                                    <p className="mt-1 text-sm text-red-500">
                                        {errors.email.message}
                                    </p>
                                )}
                            </div>

                            <div>
                                <div className="flex items-center justify-between">
                                    <label className="mb-1 block text-base font-medium text-[#666666]">
                                        Password
                                    </label>
                                    <button
                                        type="button"
                                        onClick={() =>
                                            setShowPassword(!showPassword)
                                        }
                                        className="text-sm text-gray-600"
                                    >
                                        {showPassword ? (
                                            <span className="flex items-center gap-2">
                                                <FaRegEyeSlash /> Hide
                                            </span>
                                        ) : (
                                            <span className="flex items-center gap-2">
                                                <FaRegEye /> Show
                                            </span>
                                        )}
                                    </button>
                                </div>
                                <div className="">
                                    <input
                                        type={
                                            showPassword ? 'text' : 'password'
                                        }
                                        {...register('password', {
                                            required: 'Password is required'
                                        })}
                                        className="h-12 w-full rounded-lg border border-[#666666] px-3 py-2 focus:border-black focus:outline-1"
                                    />
                                </div>
                                {errors.password && (
                                    <p className="mt-1 text-sm text-red-500">
                                        {errors.password.message}
                                    </p>
                                )}
                            </div>

                            <button
                                type="submit"
                                disabled={!isValid}
                                className={`w-full rounded-full bg-[#C3C3C3] py-3 font-semibold transition-colors ${
                                    isValid
                                        ? 'bg-gray-800 text-white hover:bg-gray-700'
                                        : 'cursor-not-allowed bg-gray-300 text-gray-500'
                                }`}
                            >
                                Log in
                            </button>
                        </form>
                    </div>

                    {/* Divider */}
                    <div className="flex items-center justify-center gap-2 md:flex-col">
                        {/* Left horizontal line for small screens */}
                        <div className="block h-0.5 w-36 bg-gray-300 md:hidden" />

                        {/* Left vertical line for medium and up */}
                        <div className="hidden h-36 w-0.5 bg-gray-300 md:block" />

                        <span className="font-medium text-gray-500">OR</span>

                        {/* Right vertical line for medium and up */}
                        <div className="hidden h-36 w-0.5 bg-gray-300 md:block" />

                        {/* Right horizontal line for small screens */}
                        <div className="block h-0.5 w-36 bg-gray-300 md:hidden" />
                    </div>

                    {/* Right - Google & Email Signup */}
                    <div className="w-4/5 space-y-4 md:w-full md:flex-1">
                        <button
                            onClick={loginWithGoogle}
                            className="flex w-full items-center justify-center gap-3 rounded-full border border-gray-400 py-3 hover:bg-gray-100"
                        >
                            <FcGoogle className="text-xl" />
                            Continue with Google
                        </button>

                        <button
                            onClick={loginWithEmail}
                            className="flex w-full items-center justify-center gap-3 rounded-full border border-gray-400 py-3 hover:bg-gray-100"
                        >
                            <MdEmail className="text-xl" />
                            Sign up with email
                        </button>
                    </div>
                </div>

                {/* Footer */}
                <div className="absolute -bottom-14 text-center text-xs text-gray-500 md:bottom-4">
                    <div className="my-4 text-center text-sm text-gray-600">
                        <Link href="#" className="underline">
                            Can't log in?
                        </Link>
                    </div>
                    Secure Login with reCAPTCHA subject to <br /> Google{' '}
                    <a href="#" className="underline">
                        Terms of Service
                    </a>{' '}
                    and{' '}
                    <a href="#" className="underline">
                        Privacy Policy
                    </a>
                </div>
            </div>
        </div>
    );
}

export default Login;
