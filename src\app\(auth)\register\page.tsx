'use client';
import Link from 'next/link';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { FaRegEye, FaRegEyeSlash } from 'react-icons/fa';
import { toast } from 'sonner';

type FormData = {
    name: string;
    username: string;
    email: string;
    password: string;
    agree: boolean;
};
function Register() {
    const {
        register,
        handleSubmit,
        reset,

        formState: { errors, isValid }
    } = useForm<FormData>({ mode: 'onChange' });
    const [showPassword, setShowPassword] = useState(false);

    const onSubmit = (data: FormData) => {
        console.log(data);
        toast.success('Registration successful!');
        reset();
    };
    return (
        <div className="flex min-h-screen items-center justify-center px-4">
            <div className="w-full max-w-screen-md">
                <div className="mb-16 mt-28 text-center">
                    <h1 className="text-2xl md:text-4xl">Create an account</h1>
                    <p className="text-base">
                        Already have an account?{' '}
                        <Link
                            href="/login"
                            className="ml-3 text-[#0088FF] underline underline-offset-4 hover:text-blue-700"
                        >
                            Log in
                        </Link>
                    </p>
                </div>

                <form
                    onSubmit={handleSubmit(onSubmit)}
                    className="space-y-6 px-6 md:px-0"
                >
                    {/* Name Field */}
                    <div className="items-start justify-between gap-10 md:flex">
                        <div className="w-full">
                            <label className="mb-1 block text-base font-medium text-black">
                                Name
                            </label>
                            <input
                                type="text"
                                {...register('name', {
                                    required: 'Name is required'
                                })}
                                className="h-12 w-full rounded-lg border border-[#666666] px-3 py-2 focus:border-black focus:outline-1"
                            />
                            {errors.name && (
                                <p className="mt-1 text-sm text-red-500">
                                    {errors.name.message}
                                </p>
                            )}
                        </div>
                        {/* username Field */}
                        <div className="mt-5 w-full md:mt-0">
                            <label className="mb-1 block text-base font-medium text-black">
                                username
                            </label>
                            <input
                                type="text"
                                {...register('username', {
                                    required: 'username is required'
                                })}
                                className="h-12 w-full rounded-lg border border-[#666666] px-3 py-2 focus:border-black focus:outline-1"
                            />
                            {errors.username && (
                                <p className="mt-1 text-sm text-red-500">
                                    {errors.username.message}
                                </p>
                            )}
                        </div>
                    </div>
                    <div className="items-center justify-between gap-4 md:flex">
                        {/* Email Field */}
                        <div className="w-full">
                            <label className="mb-1 block text-base font-medium text-black">
                                Email
                            </label>
                            <input
                                type="email"
                                {...register('email', {
                                    required: 'Email is required'
                                })}
                                className="h-12 w-full rounded-lg border border-[#666666] px-3 py-2 focus:border-black focus:outline-1"
                            />
                            {errors.email && (
                                <p className="mt-1 text-sm text-red-500">
                                    {errors.email.message}
                                </p>
                            )}
                        </div>

                        <div className="w-full"></div>
                    </div>
                    <div className="items-center justify-between gap-4 md:flex">
                        {/* Password Field */}
                        <div className="w-full">
                            <div className="flex items-center justify-between">
                                <label className="mb-1 block text-base font-medium text-black">
                                    Password
                                </label>
                                <button
                                    type="button"
                                    onClick={() =>
                                        setShowPassword(!showPassword)
                                    }
                                    className="text-sm text-gray-600"
                                >
                                    {showPassword ? (
                                        <span className="flex items-center gap-2">
                                            <FaRegEyeSlash /> Hide
                                        </span>
                                    ) : (
                                        <span className="flex items-center gap-2">
                                            <FaRegEye /> Show
                                        </span>
                                    )}
                                </button>
                            </div>
                            <input
                                type={showPassword ? 'text' : 'password'}
                                {...register('password', {
                                    required: 'Password is required'
                                })}
                                className="h-12 w-full rounded-lg border border-[#666666] px-3 py-2 focus:border-black focus:outline-1"
                            />
                            {errors.password && (
                                <p className="mt-1 text-sm text-red-500">
                                    {errors.password.message}
                                </p>
                            )}
                        </div>
                        <div className="w-full"></div>
                    </div>
                    <div className="flex items-center gap-3">
                        <input
                            type="checkbox"
                            id="agree"
                            {...register('agree', {
                                required: 'You must agree to the terms'
                            })}
                            className="size-4"
                        />
                        <label
                            htmlFor="agree"
                            className="cursor-pointer select-none text-sm text-gray-500"
                        >
                            I agree with ManageLife’s{' '}
                            <span className="underline underline-offset-2">
                                Terms and Conditions
                            </span>{' '}
                            and{' '}
                            <span className="underline underline-offset-2">
                                Privacy Policy
                            </span>
                            .
                        </label>
                    </div>

                    {/* Submit Button */}
                    <button
                        type="submit"
                        disabled={!isValid}
                        className={`w-full rounded-full py-3 font-semibold transition-colors ${
                            isValid
                                ? 'bg-gray-800 text-white hover:bg-gray-700'
                                : 'cursor-not-allowed bg-gray-300 text-gray-500'
                        }`}
                    >
                        Create Account
                    </button>
                </form>
                <div className="my-10 text-center text-sm text-gray-500">
                    <p>Secure Login with reCAPTCHA subject to </p>
                    <p className="my-1">
                        Google Terms of{' '}
                        <span className="underline">
                            Service and Privacy Policy
                        </span>
                    </p>
                </div>
            </div>
        </div>
    );
}

export default Register;
