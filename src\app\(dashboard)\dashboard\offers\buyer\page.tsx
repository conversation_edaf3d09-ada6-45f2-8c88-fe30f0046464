import DashboardCard from '@/components/dashboard/common/DashboardCard';
import Link from 'next/link';
import { IoIosArrowBack } from 'react-icons/io';

function page() {
    return (
        <div className="p-4">
            <Link
                href="/dashboard/offers"
                className="group flex items-center gap-2 font-changa text-3xl text-black hover:cursor-pointer"
            >
                <IoIosArrowBack className="group-hover:-translate-x-1 group-hover:text-accent_blue" />
                Offers: Buyer
            </Link>

            <div>
                <div className="grid grid-cols-1 gap-10 md:grid-cols-2 lg:grid-cols-3">
                    <div className="mt-16">
                        <DashboardCard
                            //  title="Buyer"
                            description={
                                <>
                                    You have {0} offers received from buyers.
                                    Create a new listing to start receiving
                                    offers. Check back to this page to view all
                                    your offers.
                                </>
                            }
                            //  linkPath="/dashboard/offers/buyer"
                            // linkText="View all"
                            btnText="Create New Listing"
                            btnPath="/dashboard/offers/create-new-listing"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}

export default page;
