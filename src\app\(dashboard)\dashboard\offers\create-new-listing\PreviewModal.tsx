'use client';

import { TListingFormInputs } from '@/type';
import { AnimatePresence, motion } from 'framer-motion';

type PreviewModalProps = {
    isOpen: boolean;
    onClose: () => void;
    data: TListingFormInputs | null;
};

const PreviewModal = ({ isOpen, onClose, data }: PreviewModalProps) => {
    if (!data) return null;

    return (
        <AnimatePresence>
            {isOpen && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        transition={{ duration: 0.3 }}
                        className="scrollbar-hidden relative max-h-[90vh] w-[700px] overflow-y-auto rounded-lg bg-white p-8 shadow-lg"
                    >
                        {/* Close Button */}
                        <button
                            onClick={onClose}
                            className="absolute right-5 top-5 select-none text-xl font-bold"
                        >
                            ✕
                        </button>

                        {/* Modal Content */}
                        <h2 className="mb-6 text-center text-2xl font-bold">
                            Listing Preview
                        </h2>

                        <div className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <p>
                                    <strong>MLS Number:</strong>{' '}
                                    {data.MLSNumber}
                                </p>
                                <p>
                                    <strong>Street Address:</strong>{' '}
                                    {data.streetAddress}
                                </p>
                                <p>
                                    <strong>Unit Number:</strong>{' '}
                                    {data.unitNumber}
                                </p>
                                <p>
                                    <strong>Zip Code:</strong> {data.zipCode}
                                </p>
                                <p>
                                    <strong>State:</strong> {data.state}
                                </p>
                            </div>

                            <div className="border-t pt-4">
                                <h4 className="mb-2 text-lg font-semibold">
                                    Financial
                                </h4>
                                <p>
                                    <strong>Price:</strong> {data.amount}{' '}
                                    {data.currency}
                                </p>
                                <p>
                                    <strong>Rent:</strong> {data.rentAmount} (
                                    {data.rentDuration})
                                </p>
                                <p>
                                    <strong>Sell as NFT:</strong>{' '}
                                    {data.sellAsNFT ? 'Yes' : 'No'}
                                </p>
                                <p>
                                    <strong>Accept Crypto:</strong>{' '}
                                    {data.acceptCrypto ? 'Yes' : 'No'}
                                </p>
                            </div>

                            <div className="border-t pt-4">
                                <h4 className="mb-2 text-lg font-semibold">
                                    Property Details
                                </h4>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <p>
                                        <strong>Parent Property:</strong>{' '}
                                        {data.parentProperty}
                                    </p>
                                    <p>
                                        <strong>Status:</strong> {data.status}
                                    </p>
                                    <p>
                                        <strong>Bedrooms:</strong>{' '}
                                        {data.bedrooms}
                                    </p>
                                    <p>
                                        <strong>Bathrooms:</strong>{' '}
                                        {data.bathrooms}
                                    </p>
                                    <p>
                                        <strong>Floors:</strong> {data.floors}
                                    </p>
                                    <p>
                                        <strong>Garages:</strong> {data.garages}
                                    </p>
                                    <p>
                                        <strong>Year Built:</strong>{' '}
                                        {data.yearBuilt}
                                    </p>
                                    <p>
                                        <strong>Home Area:</strong>{' '}
                                        {data.homeArea} sqft
                                    </p>
                                    <p>
                                        <strong>Lot Dimensions:</strong>{' '}
                                        {data.lotDimensions}
                                    </p>
                                    <p>
                                        <strong>School Ratings:</strong>{' '}
                                        {data.schoolRatings}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </div>
            )}
        </AnimatePresence>
    );
};

export default PreviewModal;
