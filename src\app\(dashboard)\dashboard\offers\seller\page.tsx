import DashboardCard from '@/components/dashboard/common/DashboardCard';
import Link from 'next/link';
import { IoIosArrowBack } from 'react-icons/io';

function OffersSeller() {
    return (
        <div className="p-4">
            <Link
                href="/dashboard/offers"
                className="group flex items-center gap-2 font-changa text-3xl text-black hover:cursor-pointer"
            >
                <IoIosArrowBack className="group-hover:-translate-x-1 group-hover:text-accent_blue" />
                Offers: Seller
            </Link>

            <div>
                <div className="grid grid-cols-1 gap-10 md:grid-cols-2 lg:grid-cols-3">
                    <div className="mt-16">
                        <DashboardCard
                            //  title="Buyer"
                            description={
                                <>
                                    As a seller, you have {0} offers sent to
                                    buyers. Create a new offers. Check back to
                                    this page to view all your offers.
                                </>
                            }
                            //  linkPath="/dashboard/offers/buyer"
                            // linkText="View all"
                            btnText="Create Offer"
                            btnPath="/dashboard/offers/create-offer"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}

export default OffersSeller;
