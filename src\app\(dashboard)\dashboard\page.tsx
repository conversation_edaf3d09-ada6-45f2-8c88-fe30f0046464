import Link from 'next/link';
import { AiOutlineUser } from 'react-icons/ai';

function DashboardHome() {
    return (
        <div>
            <h1 className="font-changa text-3xl">Dashboard </h1>
            <div className="mt-16 flex flex-col items-center justify-center gap-2">
                <AiOutlineUser size={50} />
                <h2 className="text-center font-changa text-4xl">
                    Welcome to the ManageLife Dashboard, <br />{' '}
                    <span className="text-[#0061b6]"><PERSON></span>
                </h2>
            </div>
            <div className="flex">
                <div className="mx-auto mt-16 flex h-[450px] w-[400px] flex-col items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white p-4">
                    <p>You have 0 Transactions</p>
                    <Link
                        href="/dashboard/offers/create-new-listing"
                        className="my-10 flex items-center gap-2 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                    >
                        <span className=""> Create New Listing</span>
                    </Link>
                </div>
                {/* <div className="w-1/3"></div>
                <div className="w-1/3"></div> */}
            </div>
        </div>
    );
}

export default DashboardHome;
