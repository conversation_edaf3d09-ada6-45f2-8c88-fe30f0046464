import HomeHeader from '@/components/common/HomeHeader';
import FeaturesDetails from '@/components/features/FeaturesDetails';
import FeaturesHero from '@/components/features/FeaturesHero';
import FeaturesHeroImage from '@/components/features/FeaturesHeroImage';
import {
    getAllProducts,
    getProductById
} from '@/server/actions/product/getProducts';
import { TNft } from '@/server/model/product/productType';

async function Features({ params }: any) {
    const { propertyId } = await params;
    const productById = await getProductById(propertyId);
    const allProduct = await getAllProducts({ type: 'buy' });
    // remove productById from allProduct
    const filteredProduct = allProduct.filter(
        (product) => product._id != propertyId
    );

    // console.log({ filteredProduct });
    return (
        <div className="">
            <div className="absolute left-0 z-10 w-full border-b border-black text-black md:top-10 md:border-none">
                <HomeHeader />
            </div>
            <FeaturesHero product={productById?.data as unknown as TNft} />
            <FeaturesHeroImage product={productById?.data as unknown as TNft} />
            <FeaturesDetails
                relatedProduct={filteredProduct}
                product={productById?.data as unknown as TNft}
            />
        </div>
    );
}

export default Features;
