import HomeHeader from '@/components/common/HomeHeader';
import MarketplaceComponent from '@/components/marketplace/MarketplaceComponent';
import ScrollToTop from '@/hooks/useScrollToTop';
import { getAllProducts } from '@/server/actions/product/getProducts';

const MarketPlace = async ({ searchParams }: any) => {
    const { type } = await searchParams;
    //console.log({ type });
    const products = await getAllProducts({ type });
    return (
        <div className="">
            <div className="absolute left-0 z-10 w-full border-b border-black text-black md:border-none lg:top-10">
                <HomeHeader />
            </div>
            <ScrollToTop />
            <MarketplaceComponent />
        </div>
    );
};

export default MarketPlace;
