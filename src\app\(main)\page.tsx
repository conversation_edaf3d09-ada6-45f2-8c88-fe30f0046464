import AboutMLife from '@/components/home/<USER>';
import Hero<PERSON><PERSON><PERSON> from '@/components/home/<USER>';
import HomeFAQ from '@/components/home/<USER>';
import HowItWorks from '@/components/home/<USER>';
import MeetTeam from '@/components/home/<USER>';
import WhatsMLif from '@/components/home/<USER>';

export default async function Homepage() {
    //{ searchParams }: any
    // const { type } = await searchParams;

    // const products = await getAllProducts({ type });

    return (
        <>
            <HeroSection />
            <WhatsMLif />
            <HowItWorks />
            <MeetTeam />
            <AboutMLife />
            <HomeFAQ />
            {/* <Solutions />
            <Marketplace products={products} />
            <Roadmap />
            <Tokenonmics /> */}
        </>
    );
}
