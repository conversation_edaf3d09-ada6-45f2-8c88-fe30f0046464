'use client';

import HomeHeader from '@/components/common/HomeHeader';
import { useEffect } from 'react';

export default function Whitepaper() {
    useEffect(() => {
        // Change background color of iframe container
        const iframeContainer = document.querySelector('.iframe-container');
        if (iframeContainer) {
            (iframeContainer as HTMLElement).style.backgroundColor = 'white'; // Set background color to white
        }
    }, []);

    return (
        <div className="relative">
            <div className="absolute left-0 z-10 w-full lg:top-10">
                <HomeHeader />
            </div>

            <div className="font-poppins px-4 pb-10 pt-36 sm:px-4 md:pt-[150px]">
                <h1 className="mb-10 text-center text-3xl font-bold">
                    Whitepaper
                </h1>
                {/* PDF container */}
                <div className="iframe-container h-[calc(100vh-150px)] w-full overflow-hidden rounded border bg-gray-200 shadow-lg md:mx-auto md:h-[150vh] md:max-w-[1200px]">
                    <iframe
                        src={`https://docs.google.com/gview?embedded=true&url=https://managelife.app/whitePaper/ManageLife-WhitePaper.pdf`}
                        className="h-full w-full"
                        title="Whitepaper PDF"
                        allowFullScreen
                        style={{ backgroundColor: 'transparent' }}
                    ></iframe>
                </div>
                {/* Download button */}
                <div className="mt-16 text-center">
                    <a
                        href="/whitePaper/ManageLife-WhitePaper.pdf"
                        download
                        className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
                    >
                        Download PDF
                    </a>
                </div>
            </div>
        </div>
    );
}
