import { NextRequest, NextResponse } from 'next/server';
import connectMongo from '@/server/utils/connection'; 
import WalletConnection from '@/models/WalletConnection';



// Define the request body interface
export interface WalletConnectionRequest {
  address: string;
  connectedAt: string;
}

export async function POST(req: NextRequest) {
  try {
    await connectMongo(); 

    const body: WalletConnectionRequest = await req.json();
    const { address, connectedAt } = body;

    // Validate input
    if (!address || !connectedAt) {
      return NextResponse.json(
        { message: 'Missing required fields: address or connectedAt timestamp.' },
        { status: 400 }
      );
    }

    // Validate address format (simplified check for wallet address)
    if (typeof address !== 'string' || address.length < 42 || address.length > 42) {
      return NextResponse.json(
        { message: 'Invalid wallet address format.' },
        { status: 400 }
      );
    }

    // Validate connectedAt is a valid date
    const connectedAtDate = new Date(connectedAt);
    if (isNaN(connectedAtDate.getTime())) {
      return NextResponse.json(
        { message: 'Invalid connectedAt timestamp format.' },
        { status: 400 }
      );
    }

    // Update or create wallet connection record
    const connection = await WalletConnection.findOneAndUpdate(
      { address },
      { connectedAt: new Date(connectedAt) },
      { upsert: true, new: true }
    );

    return NextResponse.json({
      message: 'Wallet connection saved successfully',
      connectionId: connection._id,
    }, { status: 201 });

  } catch (error: any) {
    console.error('API Error in /api/save-wallet-connection POST:', error);

    // Handle Mongoose validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map((err: any) => err.message);
      return NextResponse.json(
        { message: 'Validation failed.', errors: messages },
        { status: 400 }
      );
    }

    // Handle Mongoose duplicate key error (if address should be unique)
    if (error.code === 11000) {
      return NextResponse.json(
        { message: 'This wallet address is already connected.' },
        { status: 409 } // 409 Conflict
      );
    }

    // Handle database connection errors
    if (error.name === 'MongoNetworkError' || 
        (error.message && error.message.includes('ECONNREFUSED')) ||
        (error.message && error.message.toLowerCase().includes('timeout'))) {
         return NextResponse.json(
        { message: 'Database connection error. Please try again later.'},
        { status: 503 } // 503 Service Unavailable
      );
    }

    // Generic error for other issues
    return NextResponse.json(
      { message: 'Internal Server Error', error: error.message || 'An unknown error occurred.' },
      { status: 500 }
    );
  }
}