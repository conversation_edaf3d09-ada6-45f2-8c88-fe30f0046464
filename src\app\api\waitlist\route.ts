// app/api/waitlist/route.ts
import { NextRequest, NextResponse } from 'next/server';
import connectMongo from '@/server/utils/connection'; // Your Mongoose connection utility
import WaitlistEntryModel, { IWaitlistEntry } from '@/server/model/WaitlistEntry'; // Your Mongoose model

// No need for the interface here if IWaitlistEntry is imported and used correctly
// interface WaitlistEntry {
//     _id?: any;
//     role: string;
//     email: string;
//     wallet?: string;
//     dreamHome: string;
//     createdAt: Date;
// }

export async function POST(req: NextRequest) {
    try {
        // 1. Ensure MongoDB connection is established
        await connectMongo(); // This function now returns the mongoose instance,
                              // but we just need to ensure it's connected.

        const body = await req.json();
        const { role, email, wallet, dreamHome } = body;

        // --- Start Validation (Mongoose schema will also validate) ---
        // You can keep some basic pre-checks here if desired,
        // or rely more on Mongoose's built-in validation.
        if (!role || !email || !dreamHome) {
            return NextResponse.json(
                { message: 'Missing required fields: role, email, or dreamHome description.' },
                { status: 400 }
            );
        }
        // Mongoose schema already has email format validation and lowercase conversion.
        // --- End Validation ---

        // 2. Use the Mongoose model
        // Mongoose's `create` method will also perform schema validation.
        // The `save()` method on a new document instance would also work.
        
        // The `unique: true` in the schema for email handles duplicate checks.
        // If a duplicate email is submitted, Mongoose will throw an error (E11000 duplicate key error).
        // We'll catch this specific error below.

        const newEntryData: Partial<IWaitlistEntry> = { // Use Partial as _id and createdAt are auto-generated
            role,
            email, // Mongoose will lowercase it
            wallet: wallet || '',
            dreamHome,
            // createdAt is handled by default in schema
        };

        const createdEntry = await WaitlistEntryModel.create(newEntryData);
        
        return NextResponse.json(
            { message: 'Successfully joined the waitlist!', entryId: createdEntry._id },
            { status: 201 }
        );

    } catch (error: any) {
        console.error('API Error in /api/waitlist POST:', error);

        // Handle Mongoose validation errors
        if (error.name === 'ValidationError') {
            const messages = Object.values(error.errors).map((err: any) => err.message);
            return NextResponse.json(
                { message: 'Validation failed.', errors: messages },
                { status: 400 }
            );
        }

        // Handle Mongoose duplicate key error (for unique email)
        if (error.code === 11000) { // E11000 duplicate key error
            // You might want to make this message more user-friendly
            // e.g., check error.keyValue.email to confirm it's the email field
            return NextResponse.json(
                { message: 'This email address is already on the waitlist.' },
                { status: 409 } // 409 Conflict
            );
        }
        
        // Example of handling specific Mongoose connection errors (though connectMongo should handle this)
        if (error.name === 'MongoNetworkError' || 
            (error.message && error.message.includes('ECONNREFUSED')) ||
            (error.message && error.message.toLowerCase().includes('timeout'))) {
             return NextResponse.json(
                { message: 'Database connection error. Please try again later.'},
                { status: 503 } // 503 Service Unavailable
            );
        }

        // Generic error for other issues
        return NextResponse.json(
            { message: 'Internal Server Error', error: error.message || 'An unknown error occurred.' },
            { status: 500 }
        );
    }
}