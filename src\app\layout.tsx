import '@/styles/globals.css';
import { <PERSON><PERSON>_Neue, <PERSON><PERSON>_<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'next/font/google';
import localFont from 'next/font/local';
import { Providers } from './providers';
import type { Metadata } from 'next';
import { Toaster } from 'sonner';
import CookieConsent from '@/components/common/CookieConsent';

const roboto = Roboto({
    subsets: ['latin'],
    weight: ['400', '500', '700'],
    variable: '--font-roboto',
    display: 'swap'
});
const poppins = Poppins({
    subsets: ['latin'],
    weight: ['400', '500', '600', '700', '800', '900'], // adjust weights as needed
    variable: '--font-poppins',
    display: 'swap'
});
const bebasNeue = Bebas_Neue({
    subsets: ['latin'],
    weight: ['400'],
    variable: '--font-bebas-neue',
    display: 'swap'
});

const changaOne = Changa_One({
    subsets: ['latin'],
    weight: ['400'],
    variable: '--font-changa-one',
    display: 'swap'
});

const changaRegular = localFont({
    src: './fonts/ChangaOne-Regular.ttf',
    variable: '--font-changaRegular'
});
export const metadata: Metadata = {
    metadataBase: new URL('https://managelife.app'),
    title: 'ManageLife - Tokenizing Homes On-Chain',
    description:
        'ManageLife is revolutionizing home management with crypto innovation',
    authors: [{ name: 'ManageLife' }],
    keywords: ['ManageLife', 'nft'],
    openGraph: {
        title: 'ManageLife - Tokenizing Homes On-Chain',
        description:
            'ManageLife is revolutionizing home management with crypto innovation',
        images: ['/images/managelife-icon.png']
    }
};

export default function RootLayout({
    children
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html suppressHydrationWarning>
            <body
                className={`${roboto.variable} ${poppins.variable} ${bebasNeue.variable} ${changaOne.variable} ${changaRegular.variable} font-sans antialiased`}
            >
                <Toaster
                    position="top-right"
                    richColors
                    visibleToasts={1}
                    duration={1500}
                />
                <Providers>
                    {children}
                </Providers>
                {/* <SmoothScroll></SmoothScroll> */}
                <CookieConsent />
            </body>
        </html>
    );
}
