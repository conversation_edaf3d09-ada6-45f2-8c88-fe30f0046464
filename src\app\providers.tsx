'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState, useEffect, ReactNode } from 'react';
import { WagmiProvider, createConfig, http } from 'wagmi';
import { mainnet, sepolia } from 'wagmi/chains';
import { coinbaseWallet, injected, walletConnect } from 'wagmi/connectors';

// 初始化 QueryClient
const queryClient = new QueryClient();

// WalletConnect projectId
const projectId = '41b4aeb75612784f1ee6d6e19e305c8c';

// 支持的链
const chains = [mainnet, sepolia] as const;

// 配置 wagmi
const config = createConfig({
  chains,
  transports: {
    [mainnet.id]: http(),
    [sepolia.id]: http(),
  },
  connectors: [
    injected(), // MetaMask 和其他浏览器钱包
    coinbaseWallet({
      appName: 'ManageLife',
      appLogoUrl: 'https://managelife.co/images/managelife-icon.png'
    }),
    walletConnect({
      projectId,
      showQrModal: true,
      qrModalOptions: {
        themeMode: 'light'
      },
      metadata: {
        name: 'ManageLife',
        description: 'ManageLife Web3 Application',
        url: 'https://managelife.co',
        icons: ['https://managelife.co/images/managelife-icon.png']
      }
    })
  ],
});

// 主要 Provider 组件
export function Providers({ children }: { children: ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 在服务器端渲染时返回一个占位符
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <WagmiProvider config={config}>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </WagmiProvider>
  );
} 