'use client';

import Lenis from '@studio-freight/lenis';
import { ReactNode, useEffect, useRef } from 'react';

export default function SmoothScroll({ children }: { children: ReactNode }) {
    const lenisRef = useRef<Lenis | null>(null);

    useEffect(() => {
        const lenis = new Lenis({
            duration: 1.2,
            easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
        });

        const raf = (time: number) => {
            lenis.raf(time);
            requestAnimationFrame(raf);
        };

        requestAnimationFrame(raf);
        lenisRef.current = lenis;

        return () => {
            lenis.destroy();
        };
    }, []);

    return <>{children}</>;
}
