'use client';
import React from 'react'; // Import React for potential future use with more complex state/effects

export default function ComparisonSection() {
    const traditionalPoints = [
        {
            id: 1,
            text: 'Expensive: Up to 15% of home value devoured by outdated fees.',
            badHighlight: 'Up to 15%'
        },
        {
            id: 2,
            text: 'Glacial: Manual paperwork leads to agonizing, weeks-long delays.',
            badHighlight: 'weeks-long delays'
        },
        {
            id: 3,
            text: 'Restrictive: High down payments lock out a vast majority.',
            badHighlight: 'High down payments'
        },
        {
            id: 4,
            text: 'Opaque: Hidden costs and complex processes breed distrust.',
            badHighlight: 'Hidden costs'
        }
    ];

    const manageLifePoints = [
        {
            id: 1,
            mainBenefit:
                'Save <span class="text-white font-bold">$30K+</span>',
            description:
                'via ultra-low, transparent smart contract fees on the blockchain.',
            goodHighlight: '$30K+'
        },
        {
            id: 2,
            mainBenefit:
                '<span class="text-white font-bold">75%</span> Faster',
            description:
                'with automated, secure blockchain transactions. Closings in days, not months.',
            goodHighlight: '75% Faster'
        },
        {
            id: 3,
            mainBenefit:
                'Earn <span class="text-white font-bold">Rewards</span>',
            description:
                'for down payments & cost reduction through active platform engagement.',
            goodHighlight: 'Earn Rewards'
        },
        {
            id: 4,
            mainBenefit:
                '<span class="text-white font-bold">Accessible</span> Finance',
            description:
                'Flexible, crypto-powered options bypass rigid, traditional lending hurdles.',
            goodHighlight: 'Accessible'
        }
    ];

    const Icon = ({ type, className }: { type: string; className?: string }) => {
        // Using Heroicons (outline style) for a cleaner, more modern look
        if (type === 'cross') {
            return (
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className={`h-6 w-6 flex-shrink-0 ${className}`}
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"
                    />
                </svg>
            );
        }
        return (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={2}
                stroke="currentColor"
                className={`h-6 w-6 flex-shrink-0 ${className}`}
            >
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
            </svg>
        );
    };

    return (
        <section
            style={{
                backgroundImage: "url('/images/landing/about-MLife-BG-img.png')"
            }}
            className="animated-bg w-full bg-black py-24 text-white sm:py-32"
        >
            <div className="mx-auto max-w-screen-xl px-4">
                <div className="relative z-10 mx-auto mb-20 max-w-5xl text-center">
                    <h2
                        className="animate-title-fade-in font-poppins text-5xl font-extrabold text-white sm:text-6xl md:text-[80px] md:leading-[62px] [text-shadow:0_0_10px_theme(colors.purple.500),_0_0_20px_theme(colors.blue.400)]"
                        data-text="Paradigm Shift: Unveiled"
                    >
                        Paradigm Shift:{' '}
                        <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                            Unveiled
                        </span>
                    </h2>
                    <p className="animate-subtitle-fade-in mt-6 text-lg text-white/70 sm:text-xl">
                        From the archaic labyrinth to a future unbound. Witness
                        the transformation.
                    </p>
                </div>

                <div className="relative z-10 mx-auto grid max-w-7xl grid-cols-1 gap-12 px-4 md:grid-cols-2 md:gap-8 lg:gap-16">
                    {/* Traditional Real Estate Column */}
                    <div className="group animate-subtle-slide-in-left relative transform rounded-xl border border-white/10 bg-white/15 p-6 backdrop-blur-[70px] transition-all duration-500 hover:border-white/20 md:p-8">
                        <h3 className="font-poppins mb-8 text-center text-3xl font-bold text-white/80 transition-colors duration-300 group-hover:text-white sm:text-4xl">
                            The Old Maze
                        </h3>
                        <ul className="space-y-6">
                            {traditionalPoints.map(point => {
                                const [title, ...descriptionParts] =
                                    point.text.split(': ');
                                const description =
                                    descriptionParts.join(': ');
                                const highlightParts = description.split(
                                    point.badHighlight
                                );

                                return (
                                    <li
                                        key={point.id}
                                        className="flex items-start"
                                    >
                                        <Icon
                                            type="cross"
                                            className="mt-1 h-6 w-6 flex-shrink-0 text-red-400/80"
                                        />
                                        <div className="ml-4">
                                            <h4 className="text-xl font-semibold text-white/90">
                                                {title}
                                            </h4>
                                            <p className="mt-1 text-sm text-white/70">
                                                {highlightParts[0]}
                                                <span className="font-semibold text-red-400/90">
                                                    {point.badHighlight}
                                                </span>
                                                {highlightParts[1]}
                                            </p>
                                        </div>
                                    </li>
                                );
                            })}
                        </ul>
                    </div>

                    {/* With ManageLife Column */}
                    <div className="group animate-subtle-slide-in-right relative transform rounded-xl border border-white/10 bg-white/15 p-6 backdrop-blur-[70px] transition-all duration-500 hover:border-white/20 md:p-8">
                        <h3 className="font-poppins mb-8 text-center text-3xl font-bold text-white transition-colors duration-300 group-hover:text-purple-200 sm:text-4xl [text-shadow:0_0_8px_theme(colors.purple.400)]">
                            The{' '}
                            <span className="bg-gradient-to-r from-purple-300 to-sky-300 bg-clip-text text-transparent">
                                ManageLife
                            </span>{' '}
                            Future
                        </h3>
                        <ul className="space-y-6">
                            {manageLifePoints.map(point => (
                                <li
                                    key={point.id}
                                    className="group/item flex items-start"
                                >
                                    <Icon
                                        type="tick"
                                        className="mt-1 h-6 w-6 flex-shrink-0 text-green-400 group-hover/item:animate-icon-pop"
                                    />
                                    <div className="ml-4">
                                        <h4
                                            className="block text-xl font-semibold text-white"
                                            dangerouslySetInnerHTML={{
                                                __html: point.mainBenefit
                                            }}
                                        ></h4>
                                        <p className="mt-1 text-sm text-white/70">
                                            {point.description}
                                        </p>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>
            </div>
            <style jsx global>{`
                .animated-bg {
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: cover;
                    animation: bg-pan-zoom 40s linear infinite alternate;
                    overflow: hidden;
                }

                @keyframes bg-pan-zoom {
                    from {
                        background-position: 40% center;
                        transform: scale(1);
                    }
                    to {
                        background-position: 60% center;
                        transform: scale(1.1);
                    }
                }
                @keyframes title-fade-in {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .animate-title-fade-in {
                    animation: title-fade-in 1.2s
                        cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
                    opacity: 0;
                }

                @keyframes subtitle-fade-in {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .animate-subtitle-fade-in {
                    animation: subtitle-fade-in 1.2s
                        cubic-bezier(0.2, 0.8, 0.2, 1) 0.2s forwards;
                    opacity: 0;
                }

                @keyframes subtle-slide-in-left {
                    from {
                        opacity: 0;
                        transform: translateX(-20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0);
                    }
                }
                .animate-subtle-slide-in-left {
                    animation: subtle-slide-in-left 1.2s
                        cubic-bezier(0.2, 0.8, 0.2, 1) 0.4s forwards;
                    opacity: 0;
                }

                @keyframes subtle-slide-in-right {
                    from {
                        opacity: 0;
                        transform: translateX(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0);
                    }
                }
                .animate-subtle-slide-in-right {
                    animation: subtle-slide-in-right 1.2s
                        cubic-bezier(0.2, 0.8, 0.2, 1) 0.4s forwards;
                    opacity: 0;
                }

                @keyframes icon-pop {
                    0% {
                        transform: scale(1);
                    }
                    50% {
                        transform: scale(1.2);
                    }
                    100% {
                        transform: scale(1);
                    }
                }
                .animate-icon-pop {
                    animation: icon-pop 0.4s ease-out;
                }
            `}</style>
        </section>
    );
} 