import Image from 'next/image';

function FinanceSection() {
    return (
        <div className="mx-4 mt-14 max-w-[1360px] items-center justify-between md:border-2 lg:mx-auto lg:mb-64 lg:mt-24 lg:flex">
            <div className="lg:w-1/2">
                <div className="space-y-4 lg:w-[500px]">
                    <h4 className="md:font-poppins p-6 text-center text-3xl font-medium md:text-start md:font-medium lg:p-8 lg:text-[45px]">
                        Decentralized Finance
                    </h4>

                    <p className="px-4 text-center md:text-start lg:px-8">
                        Traditional finance relies on centralized institutions
                        like banks, which involve intermediaries, long
                        processes, and limited accessibility.
                    </p>
                    <p className="px-4 text-center md:text-start lg:px-8">
                        In contrast, DeFi (Decentralized Finance) uses
                        blockchain technology to offer direct, fast, and
                        transparent financial services without intermediaries,
                        providing greater control and accessibility through
                        digital platforms.
                    </p>
                    <div className="justify-self-center px-4 md:justify-self-start md:pb-6 lg:px-8">
                        <ul className="ml-7 list-disc text-lg font-semibold">
                            <li>Decentralization</li>
                            <li>Transparency</li>
                            <li>Accessibility</li>
                            <li>Efficiency</li>
                            <li>SLower Costs</li>
                            <li>User Control</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div className="mx-4 mt-8 py-6 md:mx-0 lg:mt-0 lg:w-1/2">
                <Image
                    src="/images/FinanceSection-Img.png"
                    alt="Platform"
                    width={1000}
                    height={1000}
                    className="lg:h-[524px] lg:w-[654px]"
                />
            </div>
        </div>
    );
}

export default FinanceSection;
