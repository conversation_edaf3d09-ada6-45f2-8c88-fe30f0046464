'use client';

export default function ManageLifeIntroSection() {
    return (
        <section
            style={{
                backgroundImage: "url('/images/landing/about-MLife-BG-img.png')"
            }}
            className="animated-bg w-full bg-black py-32 text-white sm:py-40 md:py-48 lg:py-56"
        >
            {/* Text Content */}
            <div className="relative z-10 mx-auto max-w-5xl px-4 text-center">
                <h2
                    className="animate-title-fade-in font-poppins text-5xl font-bold text-white [text-shadow:0_0_15px_theme(colors.purple.400),_0_0_30px_theme(colors.purple.500),_0_0_45px_theme(colors.blue.500/70)] md:text-[97px] md:font-[700] md:leading-[94px] md:tracking-[-0.05em]"
                    style={{ animationDelay: '0.1s' }}
                >
                    ManageLife.io
                </h2>
                <p
                    className="animate-subtitle-fade-in font-poppins mt-8 text-2xl font-medium text-white/80 [text-shadow:0_0_10px_theme(colors.purple.300/0.8)] sm:text-3xl md:text-4xl"
                    style={{ animationDelay: '0.2s' }}
                >
                    We{' '}
                    <span className="font-semibold text-white [text-shadow:0_0_10px_theme(colors.white),_0_0_20px_theme(colors.purple.400)]">
                        Manage
                    </span>{' '}
                    Your Life.
                </p>
            </div>
            <style jsx global>{`
                .animated-bg {
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: cover;
                    animation: bg-pan-zoom 40s linear infinite alternate;
                    overflow: hidden;
                }

                @keyframes bg-pan-zoom {
                    from {
                        background-position: 40% center;
                        transform: scale(1);
                    }
                    to {
                        background-position: 60% center;
                        transform: scale(1.1);
                    }
                }
                @keyframes title-fade-in {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .animate-title-fade-in {
                    animation: title-fade-in 1.2s
                        cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
                    opacity: 0;
                }

                @keyframes subtitle-fade-in {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .animate-subtitle-fade-in {
                    animation: subtitle-fade-in 1.2s
                        cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
                    opacity: 0;
                }
            `}</style>
        </section>
    );
} 