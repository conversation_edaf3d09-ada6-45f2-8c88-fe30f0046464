interface ManageLifeSectionProps {
    setIsHovered: (value: number) => void;
    isHovered: number;
}
const ManageLifeSection = ({
    setIsHovered,
    isHovered
}: ManageLifeSectionProps) => {
    return (
        <div className="mt-10 flex w-full max-w-[1360px] gap-2 overflow-hidden px-2 lg:mx-auto lg:my-14 lg:h-[428px] lg:gap-4 lg:px-0">
            {/* First Box */}
            <div
                onMouseOver={() => setIsHovered(1)}
                className={`flex flex-col items-center justify-center p-5 transition-all duration-500 ease-in-out lg:mx-0 lg:p-10 ${
                    isHovered === 1
                        ? 'w-1/2 cursor-pointer bg-[#F6F9FC]'
                        : 'w-1/4 border'
                }`}
            >
                <span
                    className={`font-poppins mb-6 self-end font-[900] transition-colors duration-500 lg:text-[65px] ${
                        isHovered === 1 ? 'text-black' : 'text-[#D5D5D5]'
                    }`}
                >
                    1 .
                </span>
                <div className="flex items-center justify-center lg:h-[100px]">
                    <h2
                        className={`font-poppins whitespace-nowrap text-center font-[900] text-secondary transition-opacity duration-500 lg:text-[55px] ${
                            isHovered === 1 ? 'opacity-100' : 'opacity-0'
                        }`}
                    >
                        ManageLife Platform
                    </h2>
                </div>
            </div>

            {/* Second Box */}
            <div
                onMouseOver={() => setIsHovered(2)}
                className={`flex flex-col items-center justify-center p-5 transition-all duration-500 ease-in-out lg:mx-0 lg:p-10 ${
                    isHovered === 2
                        ? 'w-1/2 cursor-pointer bg-[#F6F9FC]'
                        : 'w-1/4 border'
                }`}
            >
                <span
                    className={`font-poppins mb-6 self-end font-[900] transition-colors duration-500 lg:text-[65px] ${
                        isHovered === 2 ? 'text-black' : 'text-[#D5D5D5]'
                    }`}
                >
                    2 .
                </span>
                <div className="flex items-center justify-center lg:h-[100px]">
                    <h2
                        className={`font-poppins whitespace-nowrap text-center font-[900] text-secondary transition-opacity duration-500 lg:text-[55px] ${
                            isHovered === 2 ? 'opacity-100' : 'opacity-0'
                        }`}
                    >
                        RWA MARKETPLACE
                    </h2>
                </div>
            </div>

            {/* Third Box */}
            <div
                onMouseOver={() => setIsHovered(3)}
                className={`flex flex-col items-center justify-center p-5 transition-all duration-500 ease-in-out lg:mx-0 lg:p-10 ${
                    isHovered === 3
                        ? 'w-1/2 cursor-pointer bg-[#F6F9FC]'
                        : 'w-1/4 border'
                }`}
            >
                <span
                    className={`font-poppins mb-6 self-end font-[900] transition-colors duration-500 lg:text-[65px] ${
                        isHovered === 3 ? 'text-black' : 'text-[#D5D5D5]'
                    }`}
                >
                    3.
                </span>
                <div className="flex items-center justify-center lg:h-[100px]">
                    <h2
                        className={`font-poppins whitespace-nowrap text-center font-[900] text-secondary transition-opacity duration-500 lg:text-[53px] ${
                            isHovered === 3 ? 'opacity-100' : 'opacity-0'
                        }`}
                    >
                        Decentralized Finance
                    </h2>
                </div>
            </div>
        </div>
    );
};

export default ManageLifeSection;
