const membershipData = [
    {
        index: 1,
        title: '1. Membership',
        description:
            'Upon qualification application and contract implementation, realty budget is defined.'
    },
    {
        index: 2,
        title: '2. Flexible Home Choice',
        description:
            'House layout, yard size, preferred neighborhoods/schools, security features, local utilities, and traffic considerations are provided'
    },
    {
        index: 3,
        title: '3. Property Map',
        description:
            'A digital model of the home including appliances, electronics, hardware, and IoT smart home devices is created.'
    },
    {
        index: 4,
        title: '4. ManageLife App',
        description:
            'Streamlined home repairs, personalized shopping, and management for home, auto, family, and pets.'
    },
    {
        index: 5,
        title: '5. Last Mile Logistics',
        description:
            'Efficient delivery and logistics services for home-related needs.'
    },
    {
        index: 6,
        title: '6. On-Demand Technicians',
        description: 'Access to technicians for home maintenance and repairs.'
    },
    {
        index: 7,
        title: '7. Member Experience',
        description:
            'Enhanced user experience with personalized services and support.'
    },
    {
        index: 8,
        title: '8. Member Yields',
        description:
            'Opportunities for financial returns and rewards for members.'
    },
    {
        index: 9,
        title: '9. Lifetime Support',
        description:
            'Continuous support and services for members throughout their lifetime.'
    }
];

function Membership() {
    return (
        <div className="mx-auto mt-5 max-w-[1360px] lg:my-24">
            <div className="grid gap-4 p-6 lg:grid-cols-3 lg:p-0">
                {membershipData.map((item) => (
                    <div key={item.index} className="mb-6 border-2 p-6 md:p-8">
                        <div className="h-full text-center text-lg md:mt-20 md:text-start">
                            <p className="font-bold"> {item.title}</p>
                            <p>{item.description}</p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}

export default Membership;
