import Image from 'next/image';

function PlatformSection() {
    return (
        <div className="mx-2 mt-20 max-w-[1360px] items-center justify-between p-4 md:mt-10 md:border-2 lg:mx-auto lg:my-24 lg:flex lg:p-8">
            <div className="lg:w-1/2">
                <div className="space-y-4 lg:w-[500px]">
                    <h4 className="md:font-poppins text-center text-3xl font-medium md:font-medium lg:text-start lg:text-[40px]">
                        ManageLife Platform{' '}
                    </h4>

                    <p className="px-4 pt-4 text-center md:px-0 md:pt-0 md:text-start">
                        ManageLife is a comprehensive platform designed to
                        streamline real estate transactions and home management
                        through the integration of smart contracts and
                        decentralized finance.
                    </p>
                    <p className="px-4 py-4 text-center md:px-0 md:py-0 md:text-start">
                        Utilizing Life Coin ($MLife) as the unified currency,
                        the platform facilitates transactions, payments, and
                        ownership rewards, bridging the gap between centralized
                        and decentralized markets. The platform also offers a
                        wide array of services from flexible home choices to
                        on-demand technicians, ensuring a seamless and
                        supportive experience for members.
                    </p>
                </div>
            </div>
            <div className="mt-5 lg:mt-0 lg:w-1/2">
                <Image
                    src="/images/solutions-01.png"
                    alt="Platform"
                    width={1000}
                    height={1000}
                    className="lg:h-[524px] lg:w-[654px]"
                />
            </div>
        </div>
    );
}

export default PlatformSection;
