'use client';

import { useState } from 'react';

export default function QnaSection() {
    const faqData = [
        {
            question: "How does tokenization work on ManageLife.io?",
            answer: "Ownership or rental rights to each property are held by an LLC and represented by NFTs. Compared to traditional properties, these tokens can be traded with more flexibility and liquidity."
        },
        {
            question: "How do I purchase a house?",
            answer: "You can purchase properties directly on the ManageLife marketplace using $MLife tokens, stablecoins, or fiat. The purchase process is streamlined through smart contracts, ensuring transparency, security, and automation."
        },
        {
            question: "Are the smart contracts secure?",
            answer: "Yes, all of our smart contracts are rigorously audited by independent, third-party auditing companies to ensure they meet the security standards."
        },
        {
            question: "Is ManageLife.io regulated?",
            answer: "Yes, ManageLife.io complies with all necessary legal requirements. The tokenized assets are governed by smart contracts, while the property title transfers are handled off-chain to ensure legal compliance to the law of each state in the US."
        },
        {
            question: "What are $MLife tokens and how do they work?",
            answer: "$MLife tokens are the native tokens of the ManageLife ecosystem. They are used for rewards, incentives, and facilitating transactions on the platform. Users can earn $MLife tokens for home-related activities such as timely rent payments, property investments, and community initiatives."
        },
        {
            question: "How can I participate in the ManageLife ecosystem?",
            answer: "You can participate by buying, selling, or renting properties through the marketplace as well as making your own voice, earning $MLife tokens, staking tokens for rewards, or using DeFi financing options."
        }
    ];

    const [openIndex, setOpenIndex] = useState<number | null>(null);

    const toggleQna = (index: number) => {
        setOpenIndex(openIndex === index ? null : index);
    };

    return (
        <section className="bg-white py-16 sm:py-24">
            <div className="mx-auto max-w-3xl px-4">
                <h2 className="font-poppins mb-12 text-center text-4xl font-bold text-gray-900 sm:text-5xl">
                    Q&A
                </h2>
                <div className="space-y-6">
                    {faqData.map((item, index) => (
                        <div key={index} className="rounded-lg border border-gray-200 shadow-sm">
                            <button
                                type="button"
                                className="flex w-full items-center justify-between p-6 text-left font-medium text-gray-800 focus:outline-none"
                                onClick={() => toggleQna(index)}
                                aria-expanded={openIndex === index}
                                aria-controls={`faq-answer-${index}`}
                            >
                                <span className="text-lg">{item.question}</span>
                                <svg
                                    className={`h-6 w-6 transform transition-transform duration-200 ${openIndex === index ? 'rotate-180' : ''}`}
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            {openIndex === index && (
                                <div id={`faq-answer-${index}`} className="border-t border-gray-200 p-6">
                                    <p className="text-gray-600">{item.answer}</p>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
}; 