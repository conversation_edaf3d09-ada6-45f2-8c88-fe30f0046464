const RawMarketplaceData = [
    {
        index: 1,
        Subtitle: "Today's Home Buyers",
        title: (
            <>
                Single <br /> Ownership
            </>
        ),
        option: [
            'No flexibility',
            'Legal issues',
            'Hidden costs',
            'Overextending finances'
        ]
    },
    {
        index: 2,
        Subtitle: 'ManageLife Investor',
        title: (
            <>
                ManageLife <br /> Membership
            </>
        ),
        option: [
            'Legal FREE',
            'No hidden costs',
            'No overextending finances',
            '2% down-payment'
        ]
    },
    {
        index: 3,
        Subtitle: 'NFT Marketplace',
        title: (
            <>
                Decentralized <br /> Market
            </>
        ),
        option: [
            'Speed transactions',
            'Digital smart contract',
            'Property info',
            'Global capital'
        ]
    },
    {
        index: 4,
        Subtitle: 'NFT Owner',
        title: (
            <>
                Wealth <br /> Management
            </>
        ),
        option: [
            'Cash-flow investment',
            'Real estate collateral',
            '1:1 value',
            'Home quality'
        ]
    }
];

function RawMarketplace() {
    return (
        <div className="mx-auto my-10 max-w-[1360px] lg:my-16">
            <h4 className="font-poppins text-center text-4xl font-medium md:pb-5 md:text-[44px]">
                RWA Marketplace
            </h4>
            <p className="mx-auto max-w-[1050px] p-8 text-center lg:p-0">
                The RWA (Real World Asset) Marketplace, integrated with
                ManageLife, transforms traditional homeownership by converting
                homes into digital assets via NFTs. This system offers increased
                flexibility, transparency, and financial freedom. It addresses
                the limitations of conventional home buying, such as hidden
                costs and overextending finances, by leveraging decentralized
                markets and blockchain technology for secure, transparent, and
                efficient transactions.
            </p>
            <div className="mx-8 my-6 grid gap-4 lg:mx-0 lg:my-14 lg:grid-cols-4">
                {RawMarketplaceData.map((item) => (
                    <div
                        key={item.index}
                        className="border-2 border-white bg-[#F6F9FC] px-16 py-6 hover:border-gray-200 hover:bg-white md:px-12"
                    >
                        <div className="font-roboto pb-5 align-middle font-semibold leading-[40px] tracking-[-0.04em] lg:text-center">
                            <h4 className="pt-7 text-center text-xl md:text-start md:text-[16px]">
                                {item.Subtitle}
                            </h4>
                            <p className="font-poppins py-6 text-center text-[35px] font-semibold leading-[32px] md:text-start md:text-[30px] md:font-normal lg:py-12">
                                {item.title}
                            </p>
                        </div>
                        <ul className="list-disc pb-7 md:pb-0">
                            {item.option.map((option, index) => (
                                <li key={index} className="">
                                    {option}
                                </li>
                            ))}
                        </ul>
                    </div>
                ))}
            </div>
        </div>
    );
}

export default RawMarketplace;
