type SmallScreenSectionTitleProps = {
    index: number;
    title: string;
};

function SmallScreenSectionTitle({
    index,
    title
}: SmallScreenSectionTitleProps) {
    return (
        <div className="mx-10 my-16">
            <div className="font-poppins flex justify-center gap-4 border p-8 text-[40px] font-extrabold leading-[40px] shadow-lg">
                <p>{index}.</p>
                <h3>{title}</h3>
            </div>
        </div>
    );
}

export default SmallScreenSectionTitle;
