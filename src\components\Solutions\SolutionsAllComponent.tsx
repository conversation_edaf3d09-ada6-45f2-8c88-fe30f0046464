'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';
import FinanceSection from './FinanceSection';
import ManageLifeSection from './ManageLifeSection01';
import Membership from './Membership';
import PlatformSection from './PlatformSection';
import RawMarketplace from './RawMarketplace';
import SmallScreenSectionTitle from './SmallSceenSectionTitle';
import SolutionsHeroSection from './SolutionsHero';

function SolutionsAllComponent() {
    const [isHovered, setIsHovered] = useState(1);

    return (
        <div className="">
            <SolutionsHeroSection />
            <div className="hidden md:block">
                <ManageLifeSection
                    isHovered={isHovered}
                    setIsHovered={setIsHovered}
                />
                <AnimatePresence mode="wait">
                    {isHovered === 1 && (
                        <motion.div
                            key="section1"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ duration: 0.3, delay: 0.3 }}
                        >
                            <PlatformSection />
                            <Membership />
                        </motion.div>
                    )}

                    {isHovered === 2 && (
                        <motion.div
                            key="section2"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ duration: 0.3, delay: 0.3 }}
                        >
                            <RawMarketplace />
                        </motion.div>
                    )}

                    {isHovered === 3 && (
                        <motion.div
                            key="section3"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ duration: 0.3, delay: 0.3 }}
                        >
                            <FinanceSection />
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
            <div className="md:hidden">
                <SmallScreenSectionTitle
                    index={1}
                    title="ManageLife Platform"
                />
                <PlatformSection />
                <Membership />
                <SmallScreenSectionTitle index={2} title="RWA Marketplace" />
                <RawMarketplace />
                <SmallScreenSectionTitle
                    index={3}
                    title="Decentralized Finance"
                />
                <FinanceSection />
            </div>
        </div>
    );
}

export default SolutionsAllComponent;
