import HomeHeader from '../common/HomeHeader';

export default function SolutionsHeroSection() {
    return (
        <section className="relative min-h-[50vh] w-full bg-[#F6F9FC] md:bg-black lg:min-h-screen">
            {/* Header */}
            <div className="relative left-0 z-50 w-full bg-white lg:top-10 lg:bg-transparent">
                <HomeHeader />
            </div>

            {/* Large screen Hero Content */}
            <div className="absolute inset-0 mx-auto hidden max-w-screen-xl flex-col items-center justify-center px-4 text-white sm:items-start sm:px-0 md:flex">
                <p className="font-poppins flex w-full flex-col text-center text-4xl leading-tight sm:text-left sm:text-[82px] sm:font-[900]">
                    <span>
                        Bridging The Gap Between{' '}
                        <br className="hidden lg:block" /> Digital And
                        Real-world Assets
                    </span>
                </p>

                {/* Subtitle */}
                <div className="w-full max-w-xl pt-6 text-center text-gray_light sm:text-left">
                    <p>
                        Empowering Personal Wealth, Transforming Real Estate for
                        Every User.
                    </p>
                </div>
            </div>
            {/* Mobile screen Hero Content */}
            <div className="mx-auto flex max-w-screen-xl flex-col items-center justify-center px-4 py-32 text-black sm:items-start sm:px-0 md:hidden">
                <p className="font-poppins flex w-full flex-col text-center text-[50px] font-extrabold leading-[53px] sm:text-left sm:text-[95px]">
                    <span>
                        Bridging The Gap <br /> Between <br /> Digital And Real-{' '}
                        <br />
                        world Assets
                    </span>
                </p>

                {/* Subtitle */}
                <div className="w-full max-w-xl pt-6 text-center text-base text-black sm:text-left">
                    <p>
                        Empowering Personal Wealth, Transforming <br /> Real
                        Estate for Every User.
                    </p>
                </div>
            </div>
        </section>
    );
}
