'use client';

export default function TrappedSection() {
    const problems = [
        {
            id: 1,
            title: "Overpaying on Homes",
            mainData: "$30K",
            subTitle: "average overpayment",
            description: "Due to 8-14% in hidden transaction costs, a relic of the old system.",
            iconName: "CashIcon" // Placeholder for actual SVG icon component or path
        },
        {
            id: 2,
            title: "Down Payment Barrier",
            mainData: "$67,500",
            subTitle: "minimum for a median home*.",
            description: "While 58% of Americans have less than $5,000 in their savings accounts.",
            iconName: "SavingsIcon"
        },
        {
            id: 3,
            title: "Mortgage Rejection",
            mainData: "Denied!", // Made it more impactful
            subTitle: "credit, income, even identity doubts can derail your dream.",
            description: "Rigid traditional underwriting often fails deserving individuals.",
            iconName: "RejectedIcon"
        },
        {
            id: 4,
            title: "Lengthy Closing Times",
            mainData: "60+ Days", // Clarified 'plus'
            subTitle: "to finalize a purchase, an eternity in a fast-paced world.",
            description: "This agonizing wait introduces unnecessary uncertainty and stress.",
            iconName: "ClockIcon"
        }
    ];

    // Placeholder for SVG Icons - in a real scenario, these would be actual SVG components
    const Icon = ({ name }: { name: string }) => {
        // Simplified example - replace with actual SVG imports or a more robust icon system
        const icons: { [key: string]: React.ReactNode } = {
            CashIcon: (
                <svg
                    className="h-10 w-10 text-white/80 transition-colors duration-300 group-hover:text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="1.5"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z"
                    />
                </svg>
            ),
            SavingsIcon: (
                <svg
                    className="h-10 w-10 text-white/80 transition-colors duration-300 group-hover:text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="1.5"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M21 12a2.25 2.25 0 00-2.25-2.25H5.25A2.25 2.25 0 003 12m18 0v6.243A2.25 2.25 0 0118.75 20.25H5.25A2.25 2.25 0 013 18.243V12m18 0l-2.69-2.69A1.5 1.5 0 0015.121 9H8.879a1.5 1.5 0 00-1.06.44L3 12m18 0l-2.69-2.69A1.5 1.5 0 0015.121 9H8.879a1.5 1.5 0 00-1.06.44L3 12"
                    />
                </svg>
            ),
            RejectedIcon: (
                <svg
                    className="h-10 w-10 text-white/80 transition-colors duration-300 group-hover:text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="1.5"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"
                    />
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126z"
                    />
                </svg>
            ),
            ClockIcon: (
                <svg
                    className="h-10 w-10 text-white/80 transition-colors duration-300 group-hover:text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="1.5"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                </svg>
            )
        };
        return (
            icons[name] || (
                <div className="h-10 w-10 rounded bg-white/10"></div>
            )
        );
    };

    return (
        <section
            style={{
                backgroundImage: "url('/images/landing/about-MLife-BG-img.png')"
            }}
            className="animated-bg w-full bg-black py-24 text-white sm:py-32"
        >
            <div className="mx-auto max-w-screen-xl px-4">
                <div className="text-center">
                    <h2
                        className="animate-title-fade-in font-poppins text-5xl font-extrabold text-white [text-shadow:0_0_15px_theme(colors.purple.500),_0_0_30px_theme(colors.purple.700)] sm:text-6xl md:text-[80px] md:leading-[62px]"
                        style={{ animationDelay: '0.1s' }}
                    >
                        You Are{' '}
                        <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-purple-400 bg-clip-text text-transparent">
                            Trapped!
                        </span>
                    </h2>
                    <p
                        className="animate-subtitle-fade-in mt-6 text-lg text-white/70 sm:text-xl"
                        style={{ animationDelay: '0.2s' }}
                    >
                        The old real estate system: a maze designed to slow you
                        down.{' '}
                        <span className="font-semibold text-white/90">
                            Break free.
                        </span>
                    </p>
                </div>

                <div className="relative z-10 mx-auto mt-20 grid max-w-7xl grid-cols-1 gap-x-8 gap-y-10 px-4 sm:grid-cols-2 lg:grid-cols-4">
                    {problems.map((problem, index) => (
                        <div
                            key={problem.id}
                            style={{ animationDelay: `${0.4 + index * 0.15}s` }}
                            className="group animate-subtle-card-slide-up relative flex flex-col overflow-hidden rounded-xl border border-white/10 bg-white/15 p-6 backdrop-blur-[70px] transition-all duration-400 hover:border-white/20 hover:-translate-y-1"
                        >
                            <div className="mb-5 flex justify-start">
                                <Icon name={problem.iconName} />
                            </div>
                            <h3 className="font-poppins mb-2 text-xl font-semibold text-white transition-colors duration-300">
                                {problem.title}
                            </h3>
                            <div className="my-3 text-center">
                                <span className="font-poppins block text-5xl font-bold text-white transition-colors duration-300 md:text-6xl">
                                    {problem.mainData}
                                </span>
                                {problem.subTitle && (
                                    <p className="mt-1 text-sm text-white/60 transition-colors duration-300 group-hover:text-white/80">
                                        {problem.subTitle}
                                    </p>
                                )}
                            </div>
                            <p className="mt-auto text-sm text-white/70 transition-colors duration-300 group-hover:text-white">
                                {problem.description}
                            </p>
                        </div>
                    ))}
                </div>
            </div>
            <style jsx global>{`
                .animated-bg {
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: cover;
                    animation: bg-pan-zoom 40s linear infinite alternate;
                    overflow: hidden;
                }

                @keyframes bg-pan-zoom {
                    from {
                        background-position: 40% center;
                        transform: scale(1);
                    }
                    to {
                        background-position: 60% center;
                        transform: scale(1.1);
                    }
                }
                @keyframes title-fade-in {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .animate-title-fade-in {
                    animation: title-fade-in 1.2s
                        cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
                    opacity: 0;
                }

                @keyframes subtitle-fade-in {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .animate-subtitle-fade-in {
                    animation: subtitle-fade-in 1.2s
                        cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
                    opacity: 0;
                }

                @keyframes subtle-card-slide-up {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .animate-subtle-card-slide-up {
                    animation: subtle-card-slide-up 1s
                        cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
                    opacity: 0;
                }
            `}</style>
        </section>
    );
} 