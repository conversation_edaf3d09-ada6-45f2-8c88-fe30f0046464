'use client';

//import { ImageUploadHelper } from '@/lib/imageUpload';
import { AnimatePresence, Reorder, motion } from 'framer-motion';
import React, { useState } from 'react';
import { LuX } from 'react-icons/lu';
//import { TFormData } from '../dashboard/products/product-add/Section';

const MultipleImageField = ({
    formData,
    setFormData
}: {
    formData?: any;
    setFormData?: any;
}) => {
    const [isUploading, setIsUploading] = useState(false);
    const [isDraggingOver, setIsDraggingOver] = useState<boolean>(false);

    const removeSelectedImage: React.MouseEventHandler<
        HTMLButtonElement
    > = async (event) => {
        event.preventDefault();
        event.stopPropagation();

        const index = Number(event.currentTarget.dataset.index);

        const updatedImages = formData.images!.filter(
            (_: any, i: any) => i !== index
        );
        setFormData((prevFormData: any) => ({
            ...prevFormData,
            images: updatedImages
        }));
    };

    const handleImageChange = async (files: any) => {
        setIsUploading(true); // Start uploading state
        const uploadedImages: string[] = [];

        // for (const file of files) {
        //     const im = await ImageUploadHelper(file);
        //     uploadedImages.push(im);
        // }

        setFormData((prevFormData: any) => ({
            ...prevFormData,
            images: uploadedImages
        }));
        setIsUploading(false); // End uploading state
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDraggingOver(false);
        const files = e.dataTransfer.files;
        handleImageChange(files);
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();

        setIsDraggingOver(true);
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();

        setIsDraggingOver(false);
    };

    return (
        <div>
            <label
                htmlFor="image-upload"
                className="mb-2 inline-block cursor-pointer font-medium"
            >
                Upload photos
            </label>

            <input
                type="file"
                accept="image/*"
                onChange={(e) => handleImageChange(e.target.files as FileList)}
                style={{ display: 'none' }}
                id="image-upload"
                multiple
            />

            <div
                onClick={() => document.getElementById('image-upload')?.click()}
                className={`relative flex min-h-[12.50rem] cursor-pointer items-center justify-center rounded-md border-2 border-dashed border-slate-300 text-lg text-slate-500 ${
                    isDraggingOver ? 'bg-gray-200' : ''
                }`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
            >
                <div className="w-full py-5 text-center">
                    <div className="mb-3">
                        <svg
                            className="m-auto h-12 w-12"
                            fill="rgb(226, 232, 240)"
                            height="24"
                            stroke="currentColor"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"
                                fill="rgb(226, 232, 240)"
                                stroke="#64748b"
                            />
                            <path
                                d="M12 12v9"
                                fill="rgb(226, 232, 240)"
                                stroke="#64748b"
                            />
                            <path
                                d="m16 16-4-4-4 4"
                                fill="rgb(226, 232, 240)"
                                stroke="#64748b"
                            />
                        </svg>
                    </div>
                    <div className="my-3 w-fit justify-self-center bg-gray-300 px-5 py-3 text-center text-sm text-white">
                        Select photos
                    </div>
                    <p className="text-base">or drag photos here</p>
                    <p className="text-sm">(Up to 10 photos)</p>
                </div>
                {isUploading && (
                    <div className="absolute z-50 flex h-full w-full items-center justify-center bg-gray-300/[.5]">
                        <div className="h-10 w-10 animate-spin rounded-full border-2 border-gray-500 border-r-transparent"></div>
                    </div>
                )}
            </div>

            {formData?.images?.length !== 0 && (
                <div>
                    <h5 className="my-2">Selected Image:</h5>
                    <Reorder.Group
                        values={formData?.images || []}
                        onReorder={(v) => {
                            setFormData((prevFormData: any) => ({
                                ...prevFormData,
                                images: v
                            }));
                        }}
                        as="div"
                        axis="x"
                        style={{ overflow: 'visible' }}
                        className="flex gap-5 overflow-x-auto"
                    >
                        <AnimatePresence initial={false}>
                            {formData?.images?.map((im: any, index: any) => (
                                <Reorder.Item
                                    key={im}
                                    value={im}
                                    as="div"
                                    style={{
                                        backgroundSize: 'cover',
                                        backgroundPosition: 'center',
                                        backgroundRepeat: 'no-repeat',
                                        backgroundImage: `url(${im})`
                                    }}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{
                                        opacity: 1,
                                        backgroundColor: '#f3f3f3',
                                        y: 0,
                                        transition: { duration: 0.15 }
                                    }}
                                    exit={{
                                        opacity: 0,
                                        y: 20,
                                        transition: { duration: 0.3 }
                                    }}
                                    whileDrag={{ backgroundColor: '#e3e3e3' }}
                                    className="relative z-[1000] h-[120px] w-[120px]"
                                >
                                    {/* <motion.img
                                            key={im}
                                            src={process.env.NEXT_PUBLIC_MEDIA + im}
                                            className="w-full h-full aspect-square object-cover rounded"
                                            alt="Selected Image"
                                        /> */}
                                    <motion.button
                                        key={im}
                                        data-index={index}
                                        className="bg-red hover:bg-red focus:ring-red absolute -right-2 -top-2 rounded-full p-1 text-white focus:outline-none focus:ring"
                                        onClick={removeSelectedImage}
                                    >
                                        <LuX size={16} />
                                    </motion.button>
                                </Reorder.Item>
                            ))}
                        </AnimatePresence>
                    </Reorder.Group>
                </div>
            )}
        </div>
    );
};

export default MultipleImageField;
