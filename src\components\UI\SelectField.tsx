// components/SelectField.tsx
import React from 'react';
import { FieldError, UseFormRegister } from 'react-hook-form';

interface SelectOption {
    label: string;
    value: string;
}

interface SelectFieldProps {
    id: string;
    label: string;
    name: string;
    register: UseFormRegister<any>;
    error?: FieldError;
    requiredMessage?: string;
    options: SelectOption[];
    placeholder?: string;
}

const SelectField: React.FC<SelectFieldProps> = ({
    id,
    label,
    name,
    register,
    error,
    requiredMessage,
    options,
    placeholder
}) => {
    return (
        <div className="">
            <label htmlFor={id} className="text-sm text-[#3A3A3C]">
                {label} <span className="text-red-500">*</span>
            </label>
            <select
                id={id}
                className="h-10 w-full rounded border border-gray-300 p-2 outline-0 focus:border-gray-500"
                {...register(name, {
                    required: requiredMessage || `${label} is required`
                })}
            >
                {placeholder && (
                    <option value="" disabled hidden>
                        {placeholder}
                    </option>
                )}
                {options.map((opt) => (
                    <option key={opt.value} value={opt.value}>
                        {opt.label}
                    </option>
                ))}
            </select>
            {error && (
                <p className="mt-1 text-sm text-red-500">{error.message}</p>
            )}
        </div>
    );
};

export default SelectField;
