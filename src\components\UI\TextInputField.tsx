import { FieldError, UseFormRegister } from 'react-hook-form';

interface TextInputFieldProps {
    id: string;
    label: string;
    name: string;
    placeholder?: string;
    register: UseFormRegister<any>;
    error?: FieldError;
    requiredMessage?: string;
    type?: string;
    className?: string;
}

const TextInputField: React.FC<TextInputFieldProps> = ({
    id,
    label,
    name,
    placeholder,
    register,
    error,
    requiredMessage,
    type = 'text',
    className
}) => {
    return (
        <div className="">
            <label htmlFor={id} className="text-sm text-[#3A3A3C]">
                {label}
            </label>
            <input
                id={id}
                type={type}
                placeholder={placeholder}
                className={`h-10 w-full rounded border border-gray-300 p-2 outline-0 focus:border-gray-500 ${className || ''}`}
                {...register(name, {
                    required: requiredMessage || `${label} is required`
                })}
            />
            {error && (
                <p className="mt-1 text-sm text-red-500">{error.message}</p>
            )}
        </div>
    );
};

export default TextInputField;
