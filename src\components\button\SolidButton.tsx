// components/button/SolidButton.tsx
'use client';

import Link from 'next/link'; // If you use Next.js Link for internal navigation
import React from 'react';

interface SolidButtonProps {
    href?: string;
    className?: string;
    children: React.ReactNode;
    onClick?: (...args: any[]) => void; // 修改为接受任意参数的函数
    type?: "button" | "submit" | "reset"; // For button elements
    disabled?: boolean; // 添加 disabled 属性
}

const SolidButton: React.FC<SolidButtonProps> = ({
    href,
    className,
    children,
    onClick,
    type = "button",
    disabled = false
}) => {
    if (href && !disabled) {
        // Check if it's an external link
        const isExternal = href.startsWith('http://') || href.startsWith('https://');
        if (isExternal) {
            return (
                <a
                    href={href}
                    className={className}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={onClick} // Can also be on <a>
                >
                    {children}
                </a>
            );
        }
        // Internal link using Next.js Link
        return (
            <Link href={href} className={className} onClick={onClick}>
                 {children}
            </Link>
        );
    }

    // If no href or disabled, render a button
    return (
        <button type={type} className={className} onClick={onClick} disabled={disabled}>
            {children}
        </button>
    );
};

export default SolidButton;