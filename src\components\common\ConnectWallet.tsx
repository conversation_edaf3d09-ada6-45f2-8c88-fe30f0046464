'use client';

import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { RxCross2 } from 'react-icons/rx';
import { toast } from 'sonner';
import { useAccount, useDisconnect, useConnect } from 'wagmi';
import { injected, coinbaseWallet, walletConnect } from 'wagmi/connectors';

// WalletConnect projectId
const projectId = '41b4aeb75612784f1ee6d6e19e305c8c';

export default function ConnectWallet({
    onClose,
    isOpenWalletModal,
    onConnected
}: {
    onClose: () => void;
    isOpenWalletModal: boolean;
    onConnected?: (address: string) => void;
}) {
    const [isConnecting, setIsConnecting] = useState(false);
    const [mounted, setMounted] = useState(false);
    
    // wagmi 钩子
    const { address, isConnected } = useAccount();
    const { disconnect } = useDisconnect();
    const { connect, isPending } = useConnect();
    
    // 保存钱包连接信息到后端数据库
    const saveConnectionToBackend = async (address: string, connectedAt: Date) => {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        try {
            console.log('保存钱包连接信息到数据库...');
            const response = await fetch('/api/save-wallet-connection', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    address: address,
                    connectedAt: connectedAt.toISOString()
                }),
                signal: controller.signal
            });

            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`API 请求失败: ${response.status} ${response.statusText} - ${errorData}`);
            }
            console.log('连接信息已成功保存到数据库');

        } catch (apiError: any) {
            if (apiError.name === 'AbortError') {
                console.error('保存连接信息超时');
            } else {
                console.error(`保存连接信息失败: ${apiError.message}`);
            }
        } finally {
            clearTimeout(timeoutId);
        }
    };
    
    // 连接成功时回调
    useEffect(() => {
        if (isConnected && address && onConnected) {
            // 保存连接信息到数据库
            saveConnectionToBackend(address, new Date())
                .catch(err => console.error('保存钱包连接信息失败:', err));
            
            onConnected(address);
            toast.success('钱包连接成功!');
            
            // 连接成功后自动关闭模态框
            if (isOpenWalletModal) {
                onClose();
            }
        }
    }, [isConnected, address, onConnected, isOpenWalletModal, onClose]);

    useEffect(() => {
        setMounted(true);
        console.log('ConnectWallet 组件已挂载');
    }, []);

    // 直接使用 wagmi 连接 MetaMask
    const connectMetaMask = async () => {
        try {
            console.log('连接 MetaMask...');
            setIsConnecting(true);
            connect({ connector: injected() });
        } catch (error) {
            console.error('连接 MetaMask 失败:', error);
            toast.error('连接 MetaMask 失败');
        } finally {
            setIsConnecting(false);
        }
    };

    // 直接使用 wagmi 连接 Coinbase Wallet
    const connectCoinbase = async () => {
        try {
            console.log('连接 Coinbase Wallet...');
            setIsConnecting(true);
            connect({ 
                connector: coinbaseWallet({
                    appName: 'ManageLife',
                    appLogoUrl: 'https://managelife.io/images/managelife-icon.png'
                })
            });
        } catch (error) {
            console.error('连接 Coinbase Wallet 失败:', error);
            toast.error('连接 Coinbase Wallet 失败');
        } finally {
            setIsConnecting(false);
        }
    };

    // 直接使用 wagmi 连接 WalletConnect
    const connectWalletConnect = async () => {
        try {
            console.log('连接 WalletConnect...');
            setIsConnecting(true);
            
            // 创建并使用 WalletConnect 连接器
            const connector = walletConnect({
                projectId,
                showQrModal: true,
                metadata: {
                    name: 'ManageLife',
                    description: 'ManageLife Web3 Application',
                    url: 'https://managelife.io',
                    icons: ['https://managelife.io/images/managelife-icon.png']
                }
            });
            
            connect({ connector });
            console.log('WalletConnect 连接请求已发送');
        } catch (error) {
            console.error('连接 WalletConnect 失败:', error);
            toast.error('连接 WalletConnect 失败');
        } finally {
            setIsConnecting(false);
        }
    };

    // 断开钱包连接
    const handleDisconnect = () => {
        try {
            console.log('断开钱包连接');
            disconnect();
            toast.info('已断开钱包连接');
            if (onConnected) onConnected('');
        } catch (error) {
            console.error('断开钱包连接失败:', error);
            toast.error('断开连接失败');
        }
    };

    // 只在客户端渲染
    if (!mounted) return null;

    return (
        <AnimatePresence>
            {isOpenWalletModal && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.4 }}
                    className="fixed inset-0 z-[100] flex items-center justify-center bg-black/80 p-4"
                >
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        transition={{ duration: 0.4 }}
                        className="w-full max-w-5xl transform rounded-xl bg-white text-black shadow-xl"
                    >
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            {/* Left side */}
                            <div className="m-2 rounded-xl bg-blue-500 p-3 md:m-8 md:p-9">
                                <div className="relative rounded-xl bg-white p-6">
                                    <button
                                        className="absolute right-4 top-4 text-xl font-bold"
                                        onClick={onClose}
                                    >
                                        <RxCross2 />
                                    </button>
                                    <Image
                                        src="/images/managelife-icon.png"
                                        width={100}
                                        height={100}
                                        alt="Logo"
                                        className="mb-7 mt-5 h-10 w-10 justify-self-center"
                                    />
                                    <h2 className="mb-4 text-center text-xl font-semibold">
                                        Connect to ManageLife
                                    </h2>

                                    <div className="space-y-4">
                                        {/* MetaMask Button */}
                                        <button
                                            onClick={connectMetaMask}
                                            disabled={isConnecting || isPending}
                                            className="flex w-full items-center justify-start gap-5 rounded-full border border-gray-300 px-5 py-3 hover:bg-gray-200 md:px-9"
                                        >
                                            <Image
                                                src="/icons/metamask-icon.svg"
                                                alt="MetaMask"
                                                width={100}
                                                height={100}
                                                className="h-6 w-6"
                                            />
                                            {isConnecting || isPending ? (
                                                <>
                                                    <span className="loader" />
                                                    Connecting...
                                                </>
                                            ) : (
                                                'Connect with MetaMask'
                                            )}
                                        </button>
                                        
                                        {/* Coinbase Button */}
                                        <button
                                            onClick={connectCoinbase}
                                            disabled={isConnecting || isPending}
                                            className="flex w-full items-center justify-start gap-5 rounded-full border border-gray-300 px-5 py-3 hover:bg-gray-200 md:px-9"
                                        >
                                            <Image
                                                src="/icons/coinbase-icon.svg"
                                                alt="Coinbase"
                                                width={100}
                                                height={100}
                                                className="h-7 w-7"
                                            />
                                            {isConnecting || isPending ? (
                                                <>
                                                    <span className="loader" />
                                                    Connecting...
                                                </>
                                            ) : (
                                                'Connect with Coinbase'
                                            )}
                                        </button>
                                        
                                        {/* WalletConnect Button */}
                                        <button
                                            type="button"
                                            onClick={connectWalletConnect}
                                            disabled={isConnecting || isPending}
                                            className="flex w-full items-center justify-start gap-5 rounded-full border border-gray-300 px-5 py-3 hover:bg-gray-200 md:px-9"
                                        >
                                            <Image
                                                src="/icons/WalletConnect-icon.svg"
                                                alt="WalletConnect"
                                                width={100}
                                                height={100}
                                                className="h-5 w-6"
                                            />
                                            {isConnecting || isPending ? (
                                                <>
                                                    <span className="loader" />
                                                    Connecting...
                                                </>
                                            ) : (
                                                'Connect with WalletConnect'
                                            )}
                                        </button>
                                        
                                        {/* 如果已连接，显示断开连接按钮 */}
                                        {isConnected && address && (
                                            <div className="mt-4 flex flex-col items-center">
                                                <p className="mb-2 text-sm text-gray-700">
                                                    已连接钱包: {`${address.slice(0, 6)}...${address.slice(-4)}`}
                                                </p>
                                                <button
                                                    onClick={handleDisconnect}
                                                    className="text-sm text-red-500 hover:text-red-700"
                                                >
                                                    断开连接
                                                </button>
                                            </div>
                                        )}
                                    </div>

                                    <p className="mt-6 border-gray-300 pb-9 text-xs text-gray-600">
                                        By signing in you agree to ManageLife{' '}
                                        <Link
                                            className="underline"
                                            href="/terms"
                                        >
                                            Terms
                                        </Link>{' '}
                                        and{' '}
                                        <Link
                                            className="underline"
                                            href="/privacy"
                                        >
                                            Privacy
                                        </Link>
                                    </p>
                                </div>
                            </div>
                            {/* Right side */}
                            <div className="hidden rounded-xl bg-[#F7F8FB] p-6 lg:block">
                                <Image
                                    src="/images/connect-wallet.png"
                                    width={1000}
                                    height={1000}
                                    alt="Connect Wallet Illustration"
                                    className="mb-7 mt-5 h-[246px] w-[315px] justify-self-center"
                                />
                                <div className="mx-auto w-3/4">
                                    <h3 className="mb-9 text-start text-xl font-semibold">
                                        How to create a wallet
                                    </h3>
                                    <ul className="mb-4 list-disc space-y-1 pl-5 text-start text-base text-gray-600">
                                        <li>
                                            Unlimited projects and resources
                                        </li>
                                        <li>Unlimited templates</li>
                                        <li>Unlimited storage</li>
                                        <li>
                                            List, Board, and Calendar views...
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}