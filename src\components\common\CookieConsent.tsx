'use client';

import { useEffect, useState } from 'react';
import { BsInfoCircle } from 'react-icons/bs';
import { IoClose } from 'react-icons/io5';

export default function CookieConsent() {
    const [showBanner, setShowBanner] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [isAnimating, setIsAnimating] = useState(false);

    useEffect(() => {
        // Check if user has already made a choice
        const cookieConsent = localStorage.getItem('cookieConsent');
        if (!cookieConsent) {
            setShowBanner(true);
            // Trigger animation after a small delay to ensure DOM is ready
            setTimeout(() => {
                setIsAnimating(true);
            }, 100);
        }
        setIsLoading(false);
    }, []);

    const handleAccept = () => {
        localStorage.setItem('cookieConsent', 'accepted');
        // Animate out before hiding
        setIsAnimating(false);
        setTimeout(() => {
            setShowBanner(false);
        }, 300);

        // Here you can initialize your analytics, tracking, etc.
        console.log('Cookies accepted - Initialize tracking');
    };

    const handleDecline = () => {
        localStorage.setItem('cookieConsent', 'declined');
        // Animate out before hiding
        setIsAnimating(false);
        setTimeout(() => {
            setShowBanner(false);
        }, 300);

        // Here you can disable non-essential cookies
        console.log('Cookies declined - Disable non-essential tracking');
    };

    const handleClose = () => {
        // Treat close as decline
        handleDecline();
    };

    // Don't render anything while loading or if banner shouldn't show
    if (isLoading || !showBanner) {
        return null;
    }

    return (
        <div
            className={`fixed bottom-2 right-2 z-50 w-full max-w-xs transform transition-all duration-500 ease-out lg:bottom-4 lg:right-4 lg:max-w-sm ${
                isAnimating
                    ? 'translate-y-0 opacity-100'
                    : 'translate-y-full opacity-0'
            }`}
        >
            <div className="rounded-lg border border-gray-200 bg-white p-3 shadow-lg lg:p-6">
                <div className="flex items-start gap-2 lg:gap-3">
                    <div className="flex-shrink-0">
                        <BsInfoCircle className="h-4 w-4 text-blue-600 lg:h-6 lg:w-6" />
                    </div>
                    <div className="min-w-0 flex-1">
                        <h3 className="mb-1 text-sm font-semibold text-gray-900 lg:mb-2 lg:text-lg">
                            We use cookies
                        </h3>
                        <p className="mb-3 text-xs leading-relaxed text-gray-600 lg:mb-4 lg:text-sm">
                            We use cookies to enhance your browsing experience,
                            analyze site traffic, and personalize content. By
                            clicking "Accept All", you consent to our use of
                            cookies.
                        </p>
                        <div className="flex flex-row items-stretch justify-between gap-2 lg:items-center">
                            <button
                                onClick={handleAccept}
                                className="w-full rounded-md bg-blue-600 px-3 py-1.5 text-xs font-medium text-white transition-colors duration-200 hover:bg-blue-700 lg:px-4 lg:py-2 lg:text-sm"
                            >
                                Accept All
                            </button>
                            <button
                                onClick={handleDecline}
                                className="w-full rounded-md border border-gray-300 px-3 py-1.5 text-xs font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-50 lg:px-4 lg:py-2 lg:text-sm"
                            >
                                Decline
                            </button>
                        </div>
                        <div className="mt-2 lg:mt-3">
                            <button className="text-xs text-blue-600 underline hover:text-blue-800">
                                Cookie Policy
                            </button>
                        </div>
                    </div>
                    <button
                        onClick={handleClose}
                        className="flex-shrink-0 p-0.5 text-gray-400 transition-colors hover:text-gray-600 lg:p-1"
                        aria-label="Close cookie banner"
                    >
                        <IoClose className="h-4 w-4 lg:h-5 lg:w-5" />
                    </button>
                </div>
            </div>
        </div>
    );
}
