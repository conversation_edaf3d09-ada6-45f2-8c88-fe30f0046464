import Image from 'next/image';
import Link from 'next/link';

interface FooterItemsProps {
    title: string;
    links: { label: string; href: string }[];
}

interface SocialLinkProps {
    href: string;
    icon: string;
    alt: string;
}

const footerData: FooterItemsProps[] = [
    {
        title: 'Resources',
        links: [
            // { label: 'BLOG', href: '/blog' },
            { label: 'WHITEPAPER', href: '/whitepaper' }
        ]
    },
    {
        title: 'MANAGELIFE',
        links: [
            { label: 'SOLUTIONS', href: '/solutions' },
            { label: 'TEAM', href: '/team' }
        ]
    },
    {
        title: 'LEGAL',
        links: [
            { label: 'TERMS AND CONDITIONS', href: '/terms' },
            { label: 'PRIVACY POLICY', href: '/privacy' }
        ]
    }
];

const socialLinksData: SocialLinkProps[] = [
    {
        href: 'https://www.facebook.com/ManageLife.IO',
        icon: '/icons/fb.png',
        alt: 'Facebook'
    },
    {
        href: 'https://x.com/ManageLife_io',
        icon: '/icons/x.png',
        alt: 'Twitter'
    },
    {
        href: 'https://www.instagram.com/managelife.io/',
        icon: '/icons/ig.png',
        alt: 'Instagram'
    },
    {
        href: 'https://www.linkedin.com/company/managelife-io/',
        icon: '/icons/linkedin.png',
        alt: 'LinkedIn'
    }
];

export default function Footer() {
    const year = new Date().getFullYear();
    return (
        <>
            <section className="mx-auto max-w-screen-4xl pb-[50px] pt-[0px] sm:pb-[111px] sm:pt-[50px]">
                <div
                    style={{
                        backgroundImage: "url('/images/mlife-footer-bg.png')"
                    }}
                    className="h-full w-full bg-cover bg-no-repeat md:h-[650px]"
                >
                    <div className="mx-auto flex flex-col items-center justify-center gap-6 border-b border-black pb-10 pt-[90px] text-center sm:gap-10 md:w-[1000px] md:pt-[145px]">
                        <h2 className="font-poppins text-center text-4xl font-[600] leading-10 tracking-[-0.05em] text-black md:text-[97px] md:leading-[94px] lg:text-[97px] lg:font-[700]">
                            Reclaim Ownership, <br /> Redefined
                        </h2>
                        <p className="px-6 text-lg">
                            Build real equity. Access real homes.{' '}
                            <br className="md:hidden" /> Secure a real future.
                        </p>
                    </div>
                    <div className="mx-auto flex flex-col items-center justify-center gap-6 py-10 text-center sm:gap-10 md:w-[900px] md:py-20">
                        <p className="text-lg">
                            MLife. Real assets, transparently owned.
                        </p>
                    </div>
                </div>
            </section>
            <section className="mx-auto max-w-screen-3xl lg:px-6">
                {/* Top Footer Section */}
                <div className="flex flex-col items-center gap-8 pb-10 sm:flex-row sm:justify-between sm:gap-0 sm:pb-[123px]">
                    <div className="mx-auto hidden md:block">
                        <Image
                            src="/logo/mlife.png"
                            width={1200}
                            height={800}
                            className="h-[70px] w-[300px] md:h-[90px] md:w-[400px]"
                            alt="ManageLife Logo"
                        />
                    </div>

                    {/* Footer Links in Grid */}
                    {/* <div className="grid grid-cols-3 gap-8 p-6 text-left sm:gap-16 lg:p-0">
                        {footerData.map((data, index) => (
                            <FooterItems key={index} {...data} />
                        ))}
                    </div> */}
                </div>

                {/* Bottom Footer Section */}
                <div className="flex flex-col items-center gap-6 pb-6 sm:flex-row sm:justify-between sm:gap-0 sm:pb-10">
                    <p className="hidden text-sm sm:text-lg md:block">
                        &copy; {year} ManageLife. All Rights Reserved.
                    </p>

                    {/* Social Icons */}
                    <div className="flex items-center gap-6 sm:gap-12">
                        {socialLinksData.map((link, index) => (
                            <SocialLinks key={index} {...link} />
                        ))}
                    </div>
                </div>
                <div className="flex items-center justify-center md:hidden">
                    <Image
                        src="/logo/mlife.png"
                        width={1200}
                        height={800}
                        className="h-[50px] w-[230px] md:h-[90px] md:w-[400px]"
                        alt="ManageLife Logo"
                    />
                </div>
                <p className="mb-10 mt-3 block text-center text-sm sm:text-lg md:hidden">
                    &copy; {year} ManageLife. All Rights Reserved.
                </p>
            </section>
        </>
    );
}

function FooterItems({ title, links }: FooterItemsProps) {
    return (
        <div>
            <h3 className="mb-2 font-bebas text-xl text-tertiary sm:text-[27px]">
                {title}
            </h3>
            <ul className="space-y-1 text-sm sm:text-lg">
                {links.map((link, index) => (
                    <li key={index}>
                        <Link href={link.href} className="hover:underline">
                            {link.label}
                        </Link>
                    </li>
                ))}
            </ul>
        </div>
    );
}

function SocialLinks({ href, icon, alt }: SocialLinkProps) {
    return (
        <div className="h-6 w-6 sm:h-10 sm:w-10">
            <Link href={href} target="_blank">
                <Image
                    src={icon}
                    width={80}
                    height={80}
                    className="h-full w-full object-contain"
                    alt={alt}
                />
            </Link>
        </div>
    );
}
