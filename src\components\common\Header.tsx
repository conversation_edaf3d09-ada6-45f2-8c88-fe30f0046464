'use client';
import { navLinks } from '@/constants';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { FiMenu, FiX } from 'react-icons/fi';
import SolidButton from '../button/SolidButton';
import ConnectWallet from './ConnectWallet';

export default function Header() {
    const pathname = usePathname();
    const highlightPaths = ['/', '/solutions'];
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [isOpenWalletModal, setIsOpenWalletModal] = useState(false);

    return (
        <>
            <header className="relative mx-auto flex max-w-screen-xl items-center justify-between p-4">
                {/* Mobile Menu Button */}

                {/* Desktop Navigation */}
                <div className="hidden flex-1 lg:flex">
                    <ul className="flex items-center gap-x-3">
                        {navLinks.map((link) => (
                            <li
                                key={link.id}
                                className={`px-1 text-sm ${highlightPaths.includes(pathname) ? 'text-primary' : 'text-black'} ${
                                    link.href === pathname
                                        ? `border-b-2 ${highlightPaths.includes(pathname) ? 'border-white' : 'border-tertiary'} font-medium`
                                        : `border-b-2 ${highlightPaths.includes(pathname) ? 'border-white/0' : 'border-tertiary border-opacity-0'}`
                                }`}
                            >
                                <Link href={link.href}>{link.name}</Link>
                            </li>
                        ))}
                    </ul>
                </div>

                {/* Logo */}
                <Link
                    href="/"
                    className="hidden flex-1 lg:flex lg:justify-center"
                >
                    {highlightPaths.includes(pathname) ? (
                        <Image
                            src="/icons/logo.png"
                            width={100}
                            height={100}
                            alt="Logo"
                            className="h-10 w-10"
                        />
                    ) : (
                        <Image
                            src="/icons/Logo-black.png"
                            width={100}
                            height={100}
                            alt="Logo"
                            className="h-10 w-10"
                        />
                    )}
                </Link>

                <Link href="/" className="lg:hidden">
                    <Image
                        src="/icons/Logo-black.png"
                        width={100}
                        height={100}
                        alt="Logo"
                        className="h-10 w-10"
                    />
                </Link>
                {/* Desktop Buttons */}
                <div className="hidden flex-1 justify-end gap-x-4 lg:flex">
                    <SolidButton
                        href="/whitepaper"
                        className="flex h-[45px] w-[140px] items-center justify-center border border-tertiary bg-primary"
                    >
                        <p className="text-sm text-tertiary">Whitepaper</p>
                    </SolidButton>

                    <SolidButton
                        onClick={() => setIsOpenWalletModal(true)}
                        className="h-[45px] w-[115px] border border-primary bg-tertiary"
                    >
                        <p className="text-sm text-primary">Wallet</p>
                    </SolidButton>
                </div>
                <button
                    className={`p-2 font-bold lg:hidden ${highlightPaths.includes(pathname) ? 'text-black' : 'text-black'}`}
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                >
                    <FiMenu size={24} />
                </button>
                {/* Mobile Navigation Slider */}
                {isMobileMenuOpen && (
                    <div
                        className="fixed inset-0 z-50 bg-black bg-opacity-50"
                        onClick={() => setIsMobileMenuOpen(false)}
                    ></div>
                )}
                <div
                    className={`fixed right-0 top-0 h-full w-64 transform bg-white shadow-lg ${isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'} z-[90] p-5 transition-transform duration-500`}
                >
                    <button
                        className="absolute right-4 top-4"
                        onClick={() => setIsMobileMenuOpen(false)}
                    >
                        <FiX size={24} />
                    </button>
                    <ul className="mt-10 space-y-4">
                        {navLinks.map((link) => (
                            <li key={link.id}>
                                <Link
                                    href={link.href}
                                    className={`block w-fit px-1.5 text-lg text-black ${
                                        link.href === pathname
                                            ? 'border-b-2 border-tertiary font-medium'
                                            : 'border-b-2 border-tertiary border-opacity-0'
                                    }`}
                                    onClick={() => setIsMobileMenuOpen(false)}
                                >
                                    {link.name}
                                </Link>
                            </li>
                        ))}
                        <li>
                            <Link
                                href={'/whitepaper'}
                                className="block text-lg text-black"
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                Whitepaper
                            </Link>
                        </li>
                        <li>
                            <SolidButton
                                className="block text-lg text-black"
                                onClick={() => {
                                    setIsMobileMenuOpen(false);
                                    setIsOpenWalletModal(true);
                                }}
                            >
                                Wallet
                            </SolidButton>
                        </li>
                    </ul>
                </div>
            </header>
            {isOpenWalletModal && (
                <ConnectWallet
                    isOpenWalletModal={isOpenWalletModal}
                    onClose={() => setIsOpenWalletModal(false)}
                />
            )}
        </>
    );
}
