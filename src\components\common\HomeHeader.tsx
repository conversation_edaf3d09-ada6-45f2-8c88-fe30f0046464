'use client';
import { homeNavLinks } from '@/constants';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { FiMenu, FiX } from 'react-icons/fi';
import SolidButton from '../button/SolidButton';
import ConnectWallet from './ConnectWallet';
import { AiOutlineWallet } from 'react-icons/ai';

function shortAddress(addr: string) {
    return addr ? addr.slice(0, 6) + '...' + addr.slice(-4) : '';
}

export default function HomeHeader() {
    const pathname = usePathname();
    const highlightPaths = ['/', '/solutions'];
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [isOpenWalletModal, setIsOpenWalletModal] = useState(false);
    const [walletAddress, setWalletAddress] = useState<string | null>(null);
    const [showDropdown, setShowDropdown] = useState(false);

    return (
        <>
            <header className="relative mx-auto flex max-w-screen-xl items-center justify-between px-4 py-4 text-white lg:px-0">
                {/* Mobile Menu Button */}
                {/* Logo */}
                <Link
                    href="/"
                    className="hidden flex-1 lg:flex lg:justify-start"
                >
                    {highlightPaths.includes(pathname) ? (
                        <Image
                            src="/logo/ML-Logo.svg"
                            width={1000}
                            height={1000}
                            alt="Logo"
                            className="h-[65px] w-[195px]"
                        />
                    ) : (
                        <Image
                            src="/logo/mlife.png"
                            width={1000}
                            height={1000}
                            alt="Logo"
                            className="h-[50px] w-[195px]"
                        />
                    )}
                </Link>
                {/* Desktop Navigation */}
                <div className="hidden lg:flex">
                    <ul className="flex items-center justify-between gap-x-12">
                        {homeNavLinks.map((link) => (
                            <li
                                key={link.id}
                                className={`px-1 text-lg font-light ${highlightPaths.includes(pathname) ? 'text-primary' : 'text-black'} ${
                                    link.href === pathname
                                        ? `border-b-2 ${highlightPaths.includes(pathname) ? 'border-white' : 'border-tertiary'} font-medium`
                                        : `border-b-2 ${highlightPaths.includes(pathname) ? 'border-white/0' : 'border-tertiary border-opacity-0'}`
                                }`}
                            >
                                <Link href={link.href}>{link.name}</Link>
                            </li>
                        ))}
                    </ul>
                </div>

                <Link href="/" className="lg:hidden">
                    <Image
                        src="/images/managelife-icon.png"
                        width={100}
                        height={100}
                        alt="Logo"
                        className="h-10 w-10"
                    />
                </Link>

                {/* Desktop Buttons */}
                <div className="hidden flex-1 justify-end gap-x-4 lg:flex">
                    {walletAddress ? (
                        <div className="relative">
                            <button
                                className="flex h-[42px] w-[180px] items-center justify-center rounded-full border border-tertiary bg-primary px-4 text-lg font-light text-tertiary"
                                onClick={() => setShowDropdown((v) => !v)}
                            >
                                <AiOutlineWallet className="mr-2 h-5 w-5 text-tertiary" />
                                {shortAddress(walletAddress)}
                                <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
                            </button>
                            {showDropdown && (
                                <div className="absolute right-0 mt-2 w-40 rounded-md bg-white py-2 shadow-lg z-50">
                                    <button
                                        className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                        onClick={() => {
                                            setWalletAddress(null);
                                            setShowDropdown(false);
                                        }}
                                    >
                                        断开连接
                                    </button>
                                </div>
                            )}
                        </div>
                    ) : (
                        <SolidButton
                            onClick={() => setIsOpenWalletModal(true)}
                            className="flex h-[42px] w-[144px] items-center justify-center rounded-full border border-tertiary bg-primary"
                        >
                            <p className="text-lg font-light text-tertiary">
                                Connect
                            </p>
                        </SolidButton>
                    )}
                </div>
                <button
                    className={`p-2 font-bold lg:hidden ${highlightPaths.includes(pathname) ? 'text-white' : 'text-white'}`}
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                >
                    {pathname === '/' ? (
                        <FiMenu color="white" size={24} />
                    ) : (
                        <FiMenu color="black" size={24} />
                    )}
                </button>
                {/* Mobile Navigation Slider */}
                {isMobileMenuOpen && (
                    <div
                        className="fixed inset-0 z-50 bg-black bg-opacity-50"
                        onClick={() => setIsMobileMenuOpen(false)}
                    ></div>
                )}
                <div
                    className={`fixed right-0 top-0 h-full w-64 transform bg-white shadow-lg ${isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'} z-[90] p-5 transition-transform duration-500`}
                >
                    <button
                        className="absolute right-4 top-4"
                        onClick={() => setIsMobileMenuOpen(false)}
                    >
                        <FiX size={24} className="text-black" />
                    </button>
                    <ul className="mt-10 space-y-4">
                        {homeNavLinks.map((link) => (
                            <li key={link.id}>
                                <Link
                                    href={link.href}
                                    className={`block w-fit px-1.5 text-lg text-black ${
                                        link.href === pathname
                                            ? 'border-b-2 border-tertiary font-medium'
                                            : 'border-b-2 border-tertiary border-opacity-0'
                                    }`}
                                    onClick={() => setIsMobileMenuOpen(false)}
                                >
                                    {link.name}
                                </Link>
                            </li>
                        ))}

                        <li>
                            <SolidButton
                                className="ms-2 block text-lg text-black"
                                onClick={() => {
                                    setIsMobileMenuOpen(false);
                                    setIsOpenWalletModal(true);
                                }}
                            >
                                Connect
                            </SolidButton>
                        </li>
                    </ul>
                </div>
            </header>
            {isOpenWalletModal && (
                <ConnectWallet
                    isOpenWalletModal={isOpenWalletModal}
                    onClose={() => setIsOpenWalletModal(false)}
                    onConnected={(addr) => setWalletAddress(addr)}
                />
            )}
        </>
    );
}
