import Link from 'next/link';
type Props = {
    title?: string;
    description: string | React.ReactNode;
    linkPath?: string;
    linkText?: string;
    btnText: string;
    btnPath: string;
    className?: string;
};
function DashboardCard({
    title,
    description,
    linkPath,
    linkText,
    btnText,
    btnPath,
    className
}: Props) {
    return (
        <div>
            {title && (
                <div className="flex items-center justify-between px-2">
                    <h5 className="font-changa text-2xl">{title}</h5>
                    <Link
                        className="text-sm text-blue-500 underline underline-offset-1 hover:text-blue-700"
                        href={linkPath || ''}
                    >
                        {linkText}
                    </Link>
                </div>
            )}
            <div
                className={`mx-auto mt-2 flex h-[400px] flex-col items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white p-4 ${className || ''}`}
            >
                <p className="px-4 text-center text-sm text-[#3A3A3C]">
                    {description}
                </p>
                <Link
                    href={btnPath}
                    className="my-10 flex items-center gap-2 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                >
                    <span className=""> {btnText}</span>
                </Link>
            </div>
        </div>
    );
}

export default DashboardCard;
