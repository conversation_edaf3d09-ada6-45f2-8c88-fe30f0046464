'use client';
import { useState } from 'react';
import DashboardFooter from './DashboardFooter';
import SideMenubar from './SideMenubar';
import TopBar from './Topbar';

export default function DashboardLayout({
    children
}: {
    children: React.ReactNode;
}) {
    const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);

    return (
        <div className="flex min-h-screen">
            {/* Mobile sidebar overlay */}
            {mobileSidebarOpen && (
                <div
                    className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
                    onClick={() => setMobileSidebarOpen(false)}
                ></div>
            )}

            {/* Sidebar */}
            <div
                className={`fixed left-0 top-0 z-50 h-full w-64 flex-col bg-[#111827] text-white transition-transform duration-300 ${
                    mobileSidebarOpen
                        ? 'translate-x-0'
                        : '-translate-x-full lg:translate-x-0'
                }`}
            >
                <SideMenubar setMobileSidebarOpen={setMobileSidebarOpen} />
            </div>

            {/* Main Content */}
            <main className="flex-1 overflow-hidden bg-gray-100">
                <div className="top-0">
                    <TopBar
                        mobileSidebarOpen={mobileSidebarOpen}
                        setMobileSidebarOpen={setMobileSidebarOpen}
                    />
                </div>

                <div className="mt-20 min-h-screen p-6 lg:ms-[265px]">
                    {children}
                </div>

                <DashboardFooter />
            </main>
        </div>
    );
}
