import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';
import { AiOutlineUser } from 'react-icons/ai';
import { RxCross1 } from 'react-icons/rx';
import { menuItems } from './menuItems';

function SideMenubar({
    setMobileSidebarOpen
}: {
    setMobileSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) {
    const [activeMenu, setActiveMenu] = useState<string | null>(null);
    const pathname = usePathname();
    const router = useRouter();
    return (
        <div className="fixed left-0 top-0 h-screen bg-[#111827] md:w-64">
            {/* Logo + Close Button */}
            <div className="flex items-center justify-between border-b border-gray-800 p-3 md:flex-col md:items-center md:justify-center">
                <Link href="/">
                    <Image
                        src="/images/white-logo.svg"
                        width={1700}
                        height={1600}
                        alt="Logo"
                        className="h-10 w-auto md:h-12"
                        priority
                    />
                </Link>
                {/* Cross Button - Only on small screens */}
                <button
                    className="ml-auto flex items-center gap-2 px-3 py-2 text-sm text-white md:hidden"
                    onClick={() => setMobileSidebarOpen(false)}
                >
                    <RxCross1 size={18} />
                </button>
            </div>
            <div className="flex flex-col items-center border-b border-gray-800 px-4 py-3">
                <div className="flex w-full items-center px-4">
                    <p className="text-start font-changaRegular text-sm font-medium text-gray-400">
                        Profile
                    </p>
                </div>
                <div className="mt-1 flex items-center gap-2">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-[#777778]">
                        <AiOutlineUser className="size-7" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-400">Account</p>
                        <p className="text-sm"><EMAIL></p>
                    </div>
                </div>
            </div>
            <nav className="mt-4">
                <ul className="space-y-2 px-4">
                    <li className="px-3 font-changaRegular text-sm font-medium text-gray-400">
                        Menu
                    </li>
                    {menuItems.map((item) => {
                        const isActiveMain = pathname === item.href;
                        const isSubActive = item.subMenu?.some(
                            (sub) => pathname === sub.href
                        );
                        const isExpanded =
                            activeMenu === item.name || isSubActive;

                        return (
                            <li key={item.name}>
                                <div
                                    className={`my-1 flex cursor-pointer items-center gap-2 rounded px-3 py-2 transition-colors duration-300 hover:bg-blue-600 ${
                                        isActiveMain || isSubActive
                                            ? 'bg-blue-700 text-white'
                                            : ''
                                    }`}
                                    onClick={(e) => {
                                        e.preventDefault();
                                        setActiveMenu(
                                            activeMenu === item.name
                                                ? null
                                                : item.name
                                        );
                                        router.push(item.href, {
                                            scroll: false
                                        });
                                        setMobileSidebarOpen(false); // close on mobile after click
                                    }}
                                >
                                    <item.icon
                                        className={`${
                                            isActiveMain || isSubActive
                                                ? 'text-white'
                                                : 'text-slate-500'
                                        }`}
                                        size={18}
                                    />
                                    <span className="text-sm">{item.name}</span>
                                </div>

                                {item.subMenu && isExpanded && (
                                    <ul className="ml-6 space-y-1">
                                        {item.subMenu.map((sub) => {
                                            const isActiveSub =
                                                pathname === sub.href;
                                            return (
                                                <li key={sub.name}>
                                                    <Link href={sub.href}>
                                                        <div
                                                            className={`my-1 flex cursor-pointer items-center gap-2 rounded px-3 py-1 transition-colors duration-300 hover:bg-blue-600 ${
                                                                isActiveSub
                                                                    ? 'bg-blue-500 text-white'
                                                                    : ''
                                                            }`}
                                                            onClick={() =>
                                                                setMobileSidebarOpen(
                                                                    false
                                                                )
                                                            }
                                                        >
                                                            <span className="text-sm">
                                                                {sub.name}
                                                            </span>
                                                        </div>
                                                    </Link>
                                                </li>
                                            );
                                        })}
                                    </ul>
                                )}
                            </li>
                        );
                    })}
                </ul>
            </nav>
        </div>
    );
}

export default SideMenubar;
