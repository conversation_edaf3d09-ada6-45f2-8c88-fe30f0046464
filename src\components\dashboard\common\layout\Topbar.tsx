import { FaBars } from 'react-icons/fa';
import { IoHomeOutline } from 'react-icons/io5';

function TopBar({
    mobileSidebarOpen,
    setMobileSidebarOpen
}: {
    mobileSidebarOpen: boolean;
    setMobileSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) {
    return (
        <div className="fixed right-0 top-0 z-30 w-full">
            <div className="flex items-center justify-between bg-white p-4 shadow md:justify-end">
                {/* Mobile menu toggle */}
                <button
                    className="lg:hidden"
                    onClick={() => setMobileSidebarOpen(!mobileSidebarOpen)}
                >
                    <FaBars className="text-2xl text-gray-700" />
                </button>

                <button className="flex items-center gap-2 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600">
                    <IoHomeOutline className="text-white" />
                    <span className="border-l border-dashed border-white pl-2">
                        Sell Property
                    </span>
                </button>
            </div>
        </div>
    );
}

export default TopBar;
