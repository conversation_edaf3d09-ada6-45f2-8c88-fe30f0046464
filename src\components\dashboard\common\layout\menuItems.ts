import { AiOutlineSetting } from 'react-icons/ai';
import {
    FaRegEnvelope,
    FaRegHeart,
    FaRegUser,
    FaSignOutAlt
} from 'react-icons/fa';
import { FaPenToSquare } from 'react-icons/fa6';
import { MdInsertChartOutlined } from 'react-icons/md';

export const menuItems = [
    {
        name: 'Dashboard',
        icon: MdInsertChartOutlined,
        href: '/dashboard'
    },
    {
        name: 'Profile',
        icon: FaRegUser,
        href: '' // => /dashboard/profile
    },
    {
        name: 'Listings',
        icon: FaRegEnvelope,
        href: '' //=> /dashboard/listings
    },
    {
        name: 'Offers',
        icon: FaPenToSquare,
        href: '/dashboard/offers',
        subMenu: [
            {
                name: 'Buyer',
                href: '/dashboard/offers/buyer'
            },
            {
                name: 'Seller',
                href: '/dashboard/offers/seller'
            }
        ]
    },
    {
        name: 'Saved',
        icon: <PERSON>a<PERSON>eg<PERSON>ear<PERSON>,
        href: '' //=> /dashboard/saved
    },
    {
        name: 'Password',
        icon: AiOutlineSetting,
        href: ''
    },
    {
        name: 'Logout',
        icon: FaSignOutAlt,
        href: '' //=> /dashboard/logout
    }
];
