'use client';
import PreviewModal from '@/app/(dashboard)/dashboard/offers/create-new-listing/PreviewModal';
import MultipleImageField from '@/components/UI/MultipleImageField';
import SelectField from '@/components/UI/SelectField';
import TextInputField from '@/components/UI/TextInputField';
import { TListingFormInputs } from '@/type';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';

function CreateANewListingForm() {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [previewData, setPreviewData] = useState<TListingFormInputs | null>(
        null
    );
    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
        control,
        getValues
    } = useForm<TListingFormInputs>();

    const onSubmit = (data: any) => {
        console.log('Form Data:', data);
        // handle offer logic here
        reset();
    };

    const handleSaveAndPreview = () => {
        const formData = getValues();
        setPreviewData(formData);
        setIsModalOpen(true);
    };
    return (
        <>
            <div className="my-5">
                <form onSubmit={handleSubmit(onSubmit)} className="">
                    {/* Fill out an offer */}
                    <div className="rounded-lg bg-white px-4 py-5 shadow-md">
                        <MultipleImageField />
                    </div>{' '}
                    <div className="my-7 rounded-lg bg-white px-4 py-5 shadow-md">
                        <h6 className="text-lg font-medium">
                            Address Information
                        </h6>
                        <div className="grid grid-cols-2 items-end gap-6">
                            <div className="mt-2">
                                <TextInputField
                                    id="MLSNumber"
                                    label="MLS Number"
                                    name="MLSNumber"
                                    placeholder="Type in MLS Number"
                                    register={register}
                                    error={errors.MLSNumber}
                                    requiredMessage="MLS Number is required"
                                    className="italic"
                                />
                            </div>
                            <div className="mb-1">
                                <button
                                    type="button"
                                    className="border border-blue-500 bg-blue-500 px-5 py-2 text-sm font-medium text-white hover:bg-blue-600 hover:text-white"
                                >
                                    Check MLS
                                </button>
                            </div>
                        </div>
                        <div className="mt-4">
                            <SelectField
                                id="streetAddress"
                                label="Street address"
                                name="streetAddress"
                                register={register}
                                error={errors.streetAddress}
                                requiredMessage="Street address is required"
                                placeholder="Select a street address"
                                options={[
                                    {
                                        label: '123 Main St',
                                        value: '123 Main St'
                                    },
                                    {
                                        label: '456 Elm St',
                                        value: '456 Elm St'
                                    },
                                    {
                                        label: '789 Oak Ave',
                                        value: '789 Oak Ave'
                                    }
                                ]}
                            />
                        </div>

                        <div className="mt-4 grid grid-cols-3 gap-4">
                            <TextInputField
                                id="unitNumber"
                                label="Unit number"
                                name="unitNumber"
                                placeholder="Unit Number"
                                register={register}
                                error={errors.unitNumber}
                                requiredMessage="Unit Number is required"
                            />

                            <TextInputField
                                id="zipCode"
                                label="Zip code"
                                name="zipCode"
                                placeholder="Zip code"
                                register={register}
                                error={errors.zipCode}
                                requiredMessage="Zip code is required"
                            />

                            <SelectField
                                id="state"
                                label="State"
                                name="state"
                                register={register}
                                error={errors.state}
                                requiredMessage="State is required"
                                placeholder="Select a state"
                                options={[
                                    {
                                        label: 'California',
                                        value: 'California'
                                    },
                                    { label: 'Texas', value: 'Texas' },
                                    { label: 'Florida', value: 'Florida' },
                                    { label: 'New York', value: 'New York' },
                                    { label: 'Illinois', value: 'Illinois' }
                                ]}
                            />
                        </div>
                    </div>
                    <div className="my-7 rounded-lg bg-white px-4 py-5 shadow-md">
                        <h6 className="text-lg font-medium">Financial</h6>
                        <div className="grid grid-cols-2 items-start gap-10">
                            <div className="mt-2">
                                <p className="text-sm">Price</p>
                                <div className="flex items-center gap-2 border outline-0 focus:border-gray-500">
                                    {/* Number Input */}
                                    <Controller
                                        name="amount"
                                        control={control}
                                        rules={{ required: true }}
                                        render={({ field }) => (
                                            <input
                                                {...field}
                                                type="number"
                                                placeholder="0.00"
                                                className="w-full rounded p-2 outline-0"
                                            />
                                        )}
                                    />

                                    {/* Currency Select */}
                                    <Controller
                                        name="currency"
                                        control={control}
                                        render={({ field }) => (
                                            <select
                                                {...field}
                                                className="rounded p-2 outline-0"
                                            >
                                                <option value="ETH">ETH</option>
                                                <option value="$">$</option>
                                                <option value="€">€</option>
                                            </select>
                                        )}
                                    />
                                </div>
                            </div>

                            <div className="ms-5 mt-2">
                                <p className="text-sm">Crypto</p>
                                <div className="flex items-center gap-2">
                                    <input
                                        type="checkbox"
                                        className="size-4"
                                        name=""
                                        id=""
                                    />
                                    <label htmlFor="">Sell as NFT</label>
                                </div>
                                <div className="flex items-center gap-2">
                                    <input
                                        type="checkbox"
                                        name=""
                                        className="size-4"
                                        id=""
                                    />
                                    <label htmlFor="">
                                        I accept Crypto Currencies
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div className="grid grid-cols-2 items-end gap-10">
                            <div className="mt-2">
                                <p className="text-sm">Rent</p>
                                <div className="flex items-center gap-2 border outline-0 focus:border-gray-500">
                                    {/* Number Input */}
                                    <Controller
                                        name="amount"
                                        control={control}
                                        rules={{ required: true }}
                                        render={({ field }) => (
                                            <input
                                                {...field}
                                                type="number"
                                                placeholder="$0.00"
                                                className="w-full rounded p-2 outline-0"
                                            />
                                        )}
                                    />

                                    {/* Currency Select */}
                                    <Controller
                                        name="currency"
                                        control={control}
                                        render={({ field }) => (
                                            <select
                                                {...field}
                                                className="rounded p-2 outline-0"
                                            >
                                                {' '}
                                                <option value="month">
                                                    Per Month
                                                </option>
                                                <option value="year">
                                                    Per Year
                                                </option>
                                            </select>
                                        )}
                                    />
                                </div>
                            </div>
                            <div className="ms-5 mt-2"> </div>
                        </div>
                    </div>
                    <div className="my-7 rounded-lg bg-white px-4 py-5 shadow-md">
                        <h6 className="text-lg font-medium">
                            Property Details
                        </h6>
                        <div className="grid grid-cols-2 gap-6">
                            <SelectField
                                id="parentProperty"
                                label="Parent Property"
                                name="parentProperty"
                                register={register}
                                error={errors.parentProperty}
                                requiredMessage="Parent property is required"
                                placeholder="Select parent property"
                                options={[
                                    {
                                        label: 'Property 1',
                                        value: '123 Main St'
                                    },
                                    {
                                        label: 'Property 2',
                                        value: '456 Oak Ave'
                                    },
                                    { label: 'Property 3', value: '789 Elm St' }
                                ]}
                            />

                            <SelectField
                                id="status"
                                label="Status"
                                name="status"
                                register={register}
                                error={errors.status}
                                requiredMessage="Status is required"
                                placeholder="Select status"
                                options={[
                                    { label: 'Sell', value: 'Sell' },
                                    { label: 'Rent', value: 'Rent' },
                                    { label: 'Buy', value: 'Buy' }
                                ]}
                            />
                        </div>

                        <div className="grid grid-cols-2 items-end gap-6">
                            <div className="mt-2">
                                <TextInputField
                                    id="bedrooms"
                                    label="Bedrooms"
                                    name="bedrooms"
                                    placeholder="0"
                                    register={register}
                                    error={errors.bedrooms}
                                    requiredMessage="Bedrooms is required"
                                />
                            </div>
                            <div className="mt-2">
                                <TextInputField
                                    id="bathrooms"
                                    label="Bathrooms"
                                    name="bathrooms"
                                    placeholder="0"
                                    register={register}
                                    error={errors.bathrooms}
                                    requiredMessage="Bathrooms is required"
                                />
                            </div>
                        </div>

                        <div className="mt-4 grid grid-cols-3 gap-4">
                            <TextInputField
                                id="amount"
                                label="Floors"
                                name="amount"
                                placeholder="Floors"
                                register={register}
                                error={errors.amount}
                                requiredMessage="Floors is required"
                            />
                            <TextInputField
                                id="amount"
                                label="Garages"
                                name="amount"
                                placeholder="Garages"
                                register={register}
                                error={errors.amount}
                                requiredMessage="Garages is required"
                            />
                            <TextInputField
                                id="yearBuilt"
                                label="Year Built"
                                name="yearBuilt"
                                placeholder="Year built"
                                register={register}
                                error={errors.yearBuilt}
                                requiredMessage="Year built is required"
                            />
                        </div>
                        <div className="mt-4 grid grid-cols-3 gap-4">
                            <TextInputField
                                id="homeArea"
                                label="Home Area (sqft)"
                                name="homeArea"
                                placeholder="Home area (sqft)"
                                register={register}
                                error={errors.homeArea}
                                requiredMessage="Home area is required"
                            />

                            <TextInputField
                                id="lotDimensions"
                                label="Lot Dimensions"
                                name="lotDimensions"
                                placeholder="Lot dimensions"
                                register={register}
                                error={errors.lotDimensions}
                                requiredMessage="Lot dimensions are required"
                            />

                            <TextInputField
                                id="schoolRatings"
                                label="School Ratings"
                                name="schoolRatings"
                                placeholder="School Ratings"
                                type="text"
                                register={register}
                                error={errors.schoolRatings}
                                requiredMessage="School ratings are required"
                            />
                        </div>
                    </div>
                    <div className="flex justify-center gap-5">
                        <button
                            type="submit"
                            className="mt-5 border border-blue-500 bg-blue-500 px-5 py-3 text-sm font-medium text-white hover:bg-blue-600 hover:text-white"
                        >
                            Create New Listing
                        </button>
                        <button
                            type="button"
                            onClick={handleSaveAndPreview}
                            className="mt-5 border border-[#FFF5E0] bg-[#FFF5E0] px-5 py-3 text-sm font-medium text-black hover:bg-[#e4ce9e] hover:text-black"
                        >
                            Save & Preview
                        </button>
                    </div>
                </form>
            </div>
            <PreviewModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                data={previewData}
            />
        </>
    );
}

export default CreateANewListingForm;
