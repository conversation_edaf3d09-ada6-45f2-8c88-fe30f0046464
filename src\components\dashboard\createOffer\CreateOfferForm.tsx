'use client';
import <PERSON>Field from '@/components/UI/SelectField';
import TextInputField from '@/components/UI/TextInputField';
import { OfferFormInputs } from '@/type';
import { useForm } from 'react-hook-form';

function CreateOfferForm() {
    const {
        register,
        handleSubmit,
        formState: { errors },
        reset
    } = useForm<OfferFormInputs>();

    const onSubmit = (data: OfferFormInputs) => {
        console.log('Form Data:', data);
        // handle offer logic here
        reset();
    };
    return (
        <div className="my-5">
            <form onSubmit={handleSubmit(onSubmit)} className="">
                {/* Fill out an offer */}

                <div className="rounded-lg bg-white px-4 py-5 shadow-md">
                    <h6 className="text-lg font-medium">Fill out an offer</h6>
                    <div className="mt-2">
                        <TextInputField
                            id="buyerName"
                            label="Buyer’s Name"
                            name="buyerName"
                            placeholder="Buyer’s Name"
                            register={register}
                            error={errors.buyerName}
                        />
                    </div>
                    <div className="mt-4">
                        <TextInputField
                            id="buyerEmail"
                            label="Buyer’s Email Address"
                            name="buyerEmail"
                            type="email"
                            placeholder="Buyer’s Email Address"
                            register={register}
                            error={errors.buyerEmail}
                        />
                    </div>
                    <div className="mt-4 grid grid-cols-3 gap-4">
                        <div className="">
                            <TextInputField
                                id="sellerAgentName"
                                label="Seller Agent’s Name"
                                name="sellerAgentName"
                                placeholder="Seller Agent’s Name"
                                register={register}
                                error={errors.sellerAgentName}
                                requiredMessage="Seller Agent’s Name is required"
                            />
                        </div>
                        <div className="">
                            <TextInputField
                                id="sellerAgentEmail"
                                label="Seller Agent’s Email Address"
                                name="sellerAgentEmail"
                                type="email"
                                placeholder="Seller Agent’s Email Address"
                                register={register}
                                error={errors.sellerAgentEmail}
                                requiredMessage="Seller Agent’s Email Address is required"
                            />
                        </div>
                        <div className="">
                            <TextInputField
                                id="sellerAgentPhone"
                                label="Seller Agent’s Phone Number"
                                name="sellerAgentPhone"
                                placeholder="+****************"
                                register={register}
                                error={errors.sellerAgentPhone}
                                requiredMessage="Seller Agent’s Phone Number is required"
                            />
                        </div>
                    </div>
                </div>

                {/* Address Information */}

                <div className="my-7 rounded-lg bg-white px-4 py-5 shadow-md">
                    <h6 className="text-lg font-medium">Address Information</h6>
                    <div className="grid grid-cols-2 items-end gap-6">
                        <div className="mt-2">
                            <TextInputField
                                id="MLSNumber"
                                label="MLS Number"
                                name="MLSNumber"
                                placeholder="Type in MLS Number"
                                register={register}
                                error={errors.MLSNumber}
                                requiredMessage="MLS Number is required"
                                className="italic"
                            />
                        </div>
                        <div className="mb-1">
                            <button
                                type="button"
                                className="border border-blue-500 bg-blue-500 px-5 py-2 text-sm font-medium text-white hover:bg-blue-600 hover:text-white"
                            >
                                Check MLS
                            </button>
                        </div>
                    </div>
                    <div className="mt-4">
                        <SelectField
                            id="streetAddress"
                            label="Street address"
                            name="streetAddress"
                            register={register}
                            error={errors.streetAddress}
                            requiredMessage="Street address is required"
                            placeholder="Select a street address"
                            options={[
                                { label: '123 Main St', value: '123 Main St' },
                                { label: '456 Elm St', value: '456 Elm St' },
                                { label: '789 Oak Ave', value: '789 Oak Ave' }
                            ]}
                        />
                    </div>

                    <div className="mt-4 grid grid-cols-3 gap-4">
                        <TextInputField
                            id="unitNumber"
                            label="Unit number"
                            name="unitNumber"
                            placeholder="Unit Number"
                            register={register}
                            error={errors.unitNumber}
                            requiredMessage="Unit Number is required"
                        />

                        <TextInputField
                            id="zipCode"
                            label="Zip code"
                            name="zipCode"
                            placeholder="Zip code"
                            register={register}
                            error={errors.zipCode}
                            requiredMessage="Zip code is required"
                        />

                        <SelectField
                            id="state"
                            label="State"
                            name="state"
                            register={register}
                            error={errors.state}
                            requiredMessage="State is required"
                            placeholder="Select a state"
                            options={[
                                { label: 'California', value: 'California' },
                                { label: 'Texas', value: 'Texas' },
                                { label: 'Florida', value: 'Florida' },
                                { label: 'New York', value: 'New York' },
                                { label: 'Illinois', value: 'Illinois' }
                            ]}
                        />
                    </div>
                </div>

                {/*=================== Financial */}

                <div className="my-7 rounded-lg bg-white px-4 py-5 shadow-md">
                    <h6 className="text-lg font-medium">Financial</h6>
                    <div className="grid grid-cols-2 items-end gap-6">
                        <TextInputField
                            id="offerPrice"
                            label="Offer Price"
                            name="offerPrice"
                            placeholder="Offer Price in $"
                            register={register}
                            error={errors.offerPrice}
                            requiredMessage="Offer Price is required"
                        />

                        <div className="mt-2">
                            <TextInputField
                                id="depositPercentage"
                                label="Deposit Percent"
                                name="depositPercentage"
                                placeholder="Deposit Percent in %"
                                register={register}
                                error={errors.depositPercentage}
                                requiredMessage="Deposit Percent is required"
                            />
                        </div>
                    </div>
                    <div className="grid grid-cols-2 items-end gap-6">
                        <div className="mt-2">
                            <TextInputField
                                id="depositAmount"
                                label="Deposit Amount"
                                name="depositAmount"
                                placeholder="Deposit Amount in $"
                                register={register}
                                error={errors.depositAmount}
                                requiredMessage="Deposit Amount is required"
                            />
                        </div>
                        <div className="mt-2">
                            <SelectField
                                id="financing"
                                label="Financing Type"
                                name="financing"
                                register={register}
                                error={errors.financing}
                                requiredMessage="Financing Type is required"
                                placeholder="Select financing type"
                                options={[
                                    { label: 'Cash', value: 'Cash' },
                                    {
                                        label: 'Mortgage - 80%',
                                        value: 'Mortgage - 80%'
                                    },
                                    {
                                        label: 'Mortgage - 90%',
                                        value: 'Mortgage - 90%'
                                    },
                                    {
                                        label: 'Mortgage - 95%',
                                        value: 'Mortgage - 95%'
                                    },
                                    {
                                        label: 'Owner Financing',
                                        value: 'Owner Financing'
                                    }
                                ]}
                            />
                        </div>
                    </div>
                    <div className="grid grid-cols-3 items-end gap-6">
                        <div className="mt-2">
                            <TextInputField
                                id="loanAmount"
                                label="Loan Amount"
                                name="loanAmount"
                                placeholder="Loan Amount in $"
                                register={register}
                                error={errors.loanAmount}
                                requiredMessage="Loan Amount is required"
                            />
                        </div>
                        <div className="mt-2">
                            <SelectField
                                id="preQualified"
                                label="Pre-qualified"
                                name="preQualified"
                                register={register}
                                error={errors.preQualified}
                                requiredMessage="Pre-qualified selection is required"
                                placeholder="Yes or No"
                                options={[
                                    { label: 'Yes', value: 'yes' },
                                    { label: 'No', value: 'no' }
                                ]}
                            />
                        </div>
                    </div>

                    <div className="mt-4 grid grid-cols-3 gap-4">
                        <SelectField
                            id="additionalFinancing"
                            label="Additional Financing"
                            name="additionalFinancing"
                            register={register}
                            error={errors.additionalFinancing}
                            requiredMessage="Additional Financing is required"
                            placeholder="Select additional financing"
                            options={[
                                { label: 'None', value: 'None' },
                                { label: 'Cash', value: 'Cash' },
                                {
                                    label: 'Secondary Loan',
                                    value: 'Secondary Loan'
                                },
                                {
                                    label: 'Grant Assistance',
                                    value: 'Grant Assistance'
                                },
                                {
                                    label: 'Seller Concession',
                                    value: 'Seller Concession'
                                }
                            ]}
                        />

                        <TextInputField
                            id="amount"
                            label="Amount"
                            name="amount"
                            placeholder="Amount in %"
                            register={register}
                            error={errors.amount}
                            requiredMessage="Amount is required"
                        />

                        <TextInputField
                            id="inspection"
                            label="Inspection Contingency (days)"
                            name="inspection"
                            placeholder="Inspection Contingency"
                            register={register}
                            error={errors.inspection}
                            requiredMessage="Inspection Contingency is required"
                        />
                    </div>
                    <div className="mt-4 grid grid-cols-3 gap-4">
                        <TextInputField
                            id="financeContingency"
                            label="Finance Contingency (days)"
                            name="financeContingency"
                            placeholder="Finance Contingency"
                            register={register}
                            error={errors.financeContingency}
                            requiredMessage="Finance Contingency is required"
                        />

                        <TextInputField
                            id="appraisalContingency"
                            label="Appraisal Contingency (days)"
                            name="appraisalContingency"
                            placeholder="Appraisal Contingency"
                            register={register}
                            error={errors.appraisalContingency}
                            requiredMessage="Appraisal Contingency is required"
                        />

                        <TextInputField
                            id="closingDate"
                            label="Closing Date (days)"
                            name="closingDate"
                            placeholder="Closing Date"
                            register={register}
                            type="date"
                            error={errors.closingDate}
                            requiredMessage="Closing Date is required"
                        />
                    </div>
                    <div className="mt-4 grid grid-cols-3 gap-4">
                        <SelectField
                            id="homeWarranty"
                            label="Home Warranty"
                            name="homeWarranty"
                            options={[
                                { value: '', label: 'Select an option' },
                                {
                                    value: 'Yes - Seller Provided',
                                    label: 'Yes - Seller Provided'
                                },
                                {
                                    value: 'Yes - Buyer Provided',
                                    label: 'Yes - Buyer Provided'
                                },
                                { value: 'No', label: 'No' }
                            ]}
                            register={register}
                            error={errors.homeWarranty}
                            requiredMessage="Home Warranty is required"
                        />

                        <TextInputField
                            id="homeWarrantyAmount"
                            label="Amount"
                            name="homeWarrantyAmount"
                            placeholder="Home Warranty Amount"
                            register={register}
                            error={errors.homeWarrantyAmount}
                            requiredMessage="Home Warranty Amount is required"
                        />
                    </div>
                    <div className="mt-4 grid grid-cols-3 gap-4">
                        <SelectField
                            id="closingCost"
                            label="Closing Cost (paid by)"
                            name="closingCost"
                            options={[
                                { value: '', label: 'Select an option' },
                                { value: 'Seller', label: 'Seller' },
                                { value: 'Buyer', label: 'Buyer' },
                                { value: 'Split', label: 'Split Between Both' },
                                { value: 'Negotiable', label: 'Negotiable' }
                            ]}
                            register={register}
                            error={errors.closingCost}
                            requiredMessage="Closing Cost selection is required"
                        />

                        <TextInputField
                            id="closingCostAmount"
                            label="Amount"
                            name="closingCostAmount"
                            placeholder="Closing Cost Amount"
                            register={register}
                            error={errors.closingCostAmount}
                            requiredMessage="Closing Cost Amount is required"
                        />
                    </div>
                    <div className="mt-4 grid grid-cols-3 gap-4">
                        <SelectField
                            id="closingCost"
                            label="Closing Cost (paid by)"
                            name="closingCost"
                            options={[
                                { value: '', label: 'Select an option' },
                                { value: 'Seller', label: 'Seller' },
                                { value: 'Buyer', label: 'Buyer' },
                                { value: 'Split', label: 'Split Between Both' },
                                { value: 'Negotiable', label: 'Negotiable' }
                            ]}
                            register={register}
                            error={errors.closingCost}
                            requiredMessage="Closing Cost selection is required"
                        />

                        <TextInputField
                            id="closingCostAmount"
                            label="Amount"
                            name="closingCostAmount"
                            placeholder="Closing Cost Amount"
                            register={register}
                            error={errors.closingCostAmount}
                            requiredMessage="Closing Cost Amount is required"
                        />
                    </div>
                    <div className="mt-4 grid grid-cols-3 gap-4">
                        <TextInputField
                            id="preferredLender"
                            label="Preferred Lender"
                            name="preferredLender"
                            placeholder="Preferred Lender"
                            register={register}
                            error={errors.preferredLender}
                            requiredMessage="Preferred Lender is required"
                        />

                        <TextInputField
                            id="titleCompany"
                            label="Title Company"
                            name="titleCompany"
                            placeholder="Title Company"
                            register={register}
                            error={errors.titleCompany}
                            requiredMessage="Title Company is required"
                        />

                        <SelectField
                            id="escrowCompany"
                            label="Escrow Company"
                            name="escrowCompany"
                            options={[
                                { value: '', label: 'Select an option' },
                                { value: 'Seller', label: 'Seller' },
                                { value: 'Buyer', label: 'Buyer' },
                                { value: 'Split', label: 'Split Between Both' },
                                { value: 'Negotiable', label: 'Negotiable' }
                            ]}
                            register={register}
                            error={errors.escrowCompany}
                            requiredMessage="Escrow Company selection is required"
                        />
                    </div>
                </div>
                <div className="flex justify-center">
                    <button
                        type="submit"
                        className="mt-5 border border-blue-500 bg-blue-500 px-5 py-3 text-sm font-medium text-white hover:bg-blue-600 hover:text-white"
                    >
                        Create Offer
                    </button>
                </div>
            </form>
        </div>
    );
}

export default CreateOfferForm;
