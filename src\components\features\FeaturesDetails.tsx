'use client';
import { TNft } from '@/server/model/product/productType';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { CiCamera } from 'react-icons/ci';
import SolidButton from '../button/SolidButton';
import ImageGalleryModal from './ImageGalleryModal';

function FeaturesDetails({
    product,
    relatedProduct
}: {
    product: TNft;
    relatedProduct: TNft[];
}) {
    const [isModalOpen, setIsModalOpen] = useState<TNft | null>(null);
    const relatedProperty = relatedProduct[0];
    const tokenRate = 1636.73;
    useEffect(() => {
        if (isModalOpen) {
            document.body.classList.add('modal-open');
        } else {
            document.body.classList.remove('modal-open');
        }

        return () => {
            document.body.classList.remove('modal-open');
        };
    }, [isModalOpen]);

    return (
        <>
            <div className="mx-auto my-10 max-w-screen-xl lg:my-20">
                <div className="grid items-start gap-7 px-4 lg:grid-cols-3 lg:px-0">
                    <div className="mt-3">
                        <h5 className="mb-5 text-lg font-bold">
                            Property Facts
                        </h5>
                        <div className="flex items-center justify-between border-b-2 pb-2">
                            <div className="flex items-center gap-2 text-[#7A7474]">
                                <Image
                                    src="/images/features/icon/area.png"
                                    width={700}
                                    height={600}
                                    alt=""
                                    className="h-[32px] w-[32px] object-cover"
                                />
                                <span> Total Area</span>
                            </div>
                            <p className="font-semibold">
                                {product?.squareFeet?.toLocaleString()} sq ft
                            </p>
                        </div>
                        <div className="flex items-center justify-between border-b-2 py-2">
                            <div className="flex items-center gap-2 text-[#7A7474]">
                                <Image
                                    src="/images/features/icon/bed.png"
                                    width={700}
                                    height={600}
                                    alt=""
                                    className="h-[32px] w-[32px] object-cover"
                                />
                                <span> Bedrooms</span>
                            </div>
                            <p className="font-semibold">{product.bedrooms}</p>
                        </div>
                        <div className="flex items-center justify-between border-b-2 py-2">
                            <div className="flex items-center gap-2 text-[#7A7474]">
                                <Image
                                    src="/images/features/icon/bath-tub.png"
                                    width={700}
                                    height={600}
                                    alt=""
                                    className="h-[32px] w-[32px] object-cover"
                                />
                                <span> Bathrooms</span>
                            </div>
                            <p className="font-semibold">{product.bathrooms}</p>
                        </div>
                        <div className="flex items-center justify-between border-b-2 py-2">
                            <div className="flex items-center gap-2 text-[#7A7474]">
                                <Image
                                    src="/images/features/icon/Floor.png"
                                    width={700}
                                    height={600}
                                    alt=""
                                    className="h-[32px] w-[32px] object-cover"
                                />
                                <span> Floor</span>
                            </div>
                            <p className="font-semibold">{product.story} </p>
                        </div>
                        <div className="flex items-center justify-between border-b-2 py-2">
                            <div className="flex items-center gap-2 text-[#7A7474]">
                                <Image
                                    src="/images/features/icon/Construction.png"
                                    width={700}
                                    height={600}
                                    alt=""
                                    className="h-[32px] w-[32px] object-cover"
                                />
                                <span> Construction year</span>
                            </div>
                            <p className="font-semibold">{product.yearBuilt}</p>
                        </div>
                        <div className="mt-2 lg:mt-[60px] lg:hidden">
                            <div className="flex items-center justify-between border-b-2 pb-2">
                                <div className="flex items-center gap-2 text-[#7A7474]">
                                    <Image
                                        src="/images/features/icon/pool.png"
                                        width={700}
                                        height={600}
                                        alt=""
                                        className="h-[32px] w-[32px] object-cover"
                                    />
                                    <span> Pool</span>
                                </div>
                                <p className="font-semibold">
                                    {product.swimmingPool || 'No'}
                                </p>
                            </div>
                            <div className="flex items-center justify-between border-b-2 py-2">
                                <div className="flex items-center gap-2 text-[#7A7474]">
                                    <Image
                                        src="/images/features/icon/parking-sign.png"
                                        width={700}
                                        height={600}
                                        alt=""
                                        className="h-[32px] w-[32px] object-cover"
                                    />
                                    <span> Garage Space</span>
                                </div>
                                <p className="font-semibold">
                                    {product.garageSpace || 'N/A'}
                                </p>
                            </div>
                            <div className="flex items-center justify-between border-b-2 py-2">
                                <div className="flex items-center gap-2 text-[#7A7474]">
                                    <Image
                                        src="/images/features/icon/Rating.png"
                                        width={700}
                                        height={600}
                                        alt=""
                                        className="h-[32px] w-[32px] object-cover"
                                    />
                                    <span> School Rating</span>
                                </div>
                                <p className="font-semibold">
                                    {product.schoolRating || 'N/A'}{' '}
                                </p>
                            </div>
                            <div className="flex items-center justify-between border-b-2 py-2">
                                <div className="flex items-center gap-2 text-[#7A7474]">
                                    <Image
                                        src="/images/features/icon/house.png"
                                        width={700}
                                        height={600}
                                        alt=""
                                        className="h-[32px] w-[32px] object-cover"
                                    />
                                    <span> Standout</span>
                                </div>
                                <p className="font-semibold">
                                    {product?.standout.replace(
                                        'Residence',
                                        ''
                                    ) || 'N/A'}
                                </p>
                            </div>
                        </div>
                        <div className="my-10 space-y-3 lg:my-20">
                            <p className="text-lg font-bold">
                                Financial Details
                            </p>
                            <div>
                                {' '}
                                <p className="border-b-2 text-lg font-bold">
                                    Monthly{' '}
                                </p>
                                <div className="mt-2 flex items-center justify-between font-semibold">
                                    <p className="text-[#7A7474]">
                                        Monthly Rent
                                    </p>
                                    <p>
                                        {product?.monthlyRevenue?.toLocaleString()}
                                    </p>
                                </div>
                                <div className="my-2 flex items-center justify-between font-semibold">
                                    <p className="text-[#7A7474]">
                                        Monthly Expense{' '}
                                    </p>
                                    <p>
                                        {product?.monthlyExpense?.toLocaleString()}
                                    </p>
                                </div>
                                <div className="flex items-center justify-between font-semibold">
                                    <p className="text-[#7A7474]">
                                        Monthly Net Cashflow
                                    </p>
                                    <p>
                                        {(
                                            product?.annualNetCashflow / 12
                                        )?.toFixed(2)}
                                    </p>
                                </div>
                            </div>
                            <div>
                                {' '}
                                <p className="border-b-2 text-lg font-bold">
                                    Cash on Cash
                                </p>
                                <div className="mt-2 flex items-center justify-between font-semibold">
                                    <p className="text-[#7A7474]">
                                        Cash on Cash
                                    </p>
                                    <p>{product?.cashOnCash?.toFixed(2)}%</p>
                                </div>
                            </div>
                            <div>
                                {' '}
                                <p className="border-b-2 text-lg font-bold">
                                    Annual Appreciation Rate
                                </p>
                                <div className="mt-2 flex items-center justify-between font-semibold">
                                    <p className="text-[#7A7474]">
                                        Annual Appreciation Rate
                                    </p>
                                    <p>
                                        {product?.annualAppreciationRate?.toFixed(
                                            2
                                        )}
                                        %
                                    </p>
                                </div>
                            </div>
                            <div>
                                {' '}
                                <p className="border-b-2 text-lg font-bold">
                                    Other
                                </p>
                                <div className="mt-2 flex items-center justify-between font-semibold">
                                    <p className="text-[#7A7474]">
                                        Property Tax
                                    </p>
                                    <p>
                                        {product?.propertyTax?.toLocaleString()}
                                    </p>
                                </div>
                                <div className="mt-2 flex items-center justify-between font-semibold">
                                    <p className="text-[#7A7474]">HOA</p>
                                    <p>{product?.hoa || '0'}</p>
                                </div>
                                <div className="mt-2 flex items-center justify-between font-semibold">
                                    <p className="text-[#7A7474]">
                                        Maintenance
                                    </p>
                                    <p>
                                        {product?.maintenance?.toLocaleString()}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="hidden lg:block">
                            <SolidButton className="group h-[45px] w-full rounded-md border border-primary bg-tertiary transition-colors duration-500 hover:border-tertiary hover:bg-primary">
                                <p className="text-sm text-primary group-hover:text-tertiary">
                                    Place a Bid
                                </p>
                            </SolidButton>
                        </div>
                    </div>
                    <div className="mt-[60px] hidden lg:block">
                        <div className="flex items-center justify-between border-b-2 pb-2">
                            <div className="flex items-center gap-2 text-[#7A7474]">
                                <Image
                                    src="/images/features/icon/pool.png"
                                    width={700}
                                    height={600}
                                    alt=""
                                    className="h-[32px] w-[32px] object-cover"
                                />
                                <span> Pool</span>
                            </div>
                            <p className="font-semibold">
                                {product.swimmingPool || 'No'}
                            </p>
                        </div>
                        <div className="flex items-center justify-between border-b-2 py-2">
                            <div className="flex items-center gap-2 text-[#7A7474]">
                                <Image
                                    src="/images/features/icon/parking-sign.png"
                                    width={700}
                                    height={600}
                                    alt=""
                                    className="h-[32px] w-[32px] object-cover"
                                />
                                <span> Garage Space</span>
                            </div>
                            <p className="font-semibold">
                                {product.garageSpace || 'N/A'}
                            </p>
                        </div>
                        <div className="flex items-center justify-between border-b-2 py-2">
                            <div className="flex items-center gap-2 text-[#7A7474]">
                                <Image
                                    src="/images/features/icon/Rating.png"
                                    width={700}
                                    height={600}
                                    alt=""
                                    className="h-[32px] w-[32px] object-cover"
                                />
                                <span> School Rating</span>
                            </div>
                            <p className="font-semibold">
                                {product.schoolRating || 'N/A'}
                            </p>
                        </div>
                        <div className="flex items-center justify-between border-b-2 py-2">
                            <div className="flex items-center gap-2 text-[#7A7474]">
                                <Image
                                    src="/images/features/icon/house.png"
                                    width={700}
                                    height={600}
                                    alt=""
                                    className="h-[32px] w-[32px] object-cover"
                                />
                                <span> Standout</span>
                            </div>
                            <p className="font-semibold">
                                {product.standout.replace('Residence', ' ') ||
                                    'N/A'}
                            </p>
                        </div>
                    </div>
                    <div className="">
                        {/* Related property */}
                        {/* <div className="rounded-md shadow">
                            <div className="p-4">
                                <Link
                                    href={`/marketplace/${relatedProperty?._id}`}
                                >
                                    <h5 className="overflow-hidden text-xl font-bold">
                                        Related property
                                    </h5>
                                    <p className="mb-3 line-clamp-2 text-sm">
                                        {relatedProperty?.description}
                                    </p>
                                    <Image
                                        src={relatedProperty?.image}
                                        width={700}
                                        height={600}
                                        alt=""
                                        className="h-[50%] w-full rounded-lg object-cover"
                                    />
                                    <div className="mt-4">
                                        <p className="font-semibold">
                                            Property in{' '}
                                            {product?.property?.city}
                                        </p>
                                        <p className="my-2 text-base text-[#7A7474]">
                                            {product?.nftName}{' '}
                                            {product?.property?.city},{' '}
                                            {product?.property?.state} -
                                            {product?.property?.zipCode}
                                        </p>
                                        <h6 className="font-semibold">
                                            {(
                                                product?.price / tokenRate
                                            )?.toFixed(2) || 0}{' '}
                                            ETH
                                        </h6>
                                        <hr className="my-2" />
                                        <div className="flex items-center justify-between">
                                            <div>
                                                <div className="flex items-center space-x-2">
                                                    <Image
                                                        src="/images/features/icon/bed.png"
                                                        width={700}
                                                        height={600}
                                                        alt=""
                                                        className="h-[24px] w-[24px] object-cover"
                                                    />
                                                    <span>
                                                        {product?.bedrooms}
                                                    </span>
                                                </div>
                                                <p className="text-sm">
                                                    Bedrooms
                                                </p>
                                            </div>
                                            <div>
                                                <div className="flex items-center space-x-2">
                                                    <Image
                                                        src="/images/features/icon/bath-tub.png"
                                                        width={700}
                                                        height={600}
                                                        alt=""
                                                        className="h-[24px] w-[24px] object-cover"
                                                    />
                                                    <span>
                                                        {product?.bathrooms}
                                                    </span>
                                                </div>
                                                <p className="text-sm">
                                                    Bathrooms
                                                </p>
                                            </div>
                                            <div>
                                                <div className="flex items-center space-x-2">
                                                    <Image
                                                        src="/images/features/icon/area.png"
                                                        width={700}
                                                        height={600}
                                                        alt=""
                                                        className="h-[24px] w-[24px] object-cover"
                                                    />
                                                    <span>
                                                        {product?.squareFeet}
                                                    </span>
                                                </div>
                                                <p className="text-sm">
                                                    Total area
                                                </p>
                                            </div>
                                            <div>
                                                <div className="flex items-center space-x-2">
                                                    <Image
                                                        src="/images/features/icon/garages.png"
                                                        width={700}
                                                        height={600}
                                                        alt=""
                                                        className="h-[24px] w-[24px] object-cover"
                                                    />
                                                    <span>
                                                        {product?.garageSpace}
                                                    </span>
                                                </div>
                                                <p className="text-sm">
                                                    Garages
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            </div>
                        </div> */}
                        {/* Featured Listings */}
                        <div className="rounded-md shadow">
                            <div className="mt-10 px-3 pb-1 pt-3">
                                {' '}
                                <h5 className="text-xl font-bold">
                                    Featured Listings
                                </h5>
                                {relatedProduct
                                    ?.slice(2, 6)
                                    .map((item: TNft) => (
                                        <div
                                            key={item._id}
                                            className="flex items-center py-1"
                                        >
                                            <Image
                                                src={item.otherImages[0]}
                                                width={700}
                                                height={600}
                                                alt=""
                                                className="h-[110px] w-[160px] object-fill"
                                            />
                                            <div className="mx-4">
                                                <Link
                                                    href={`/marketplace/${item._id}`}
                                                    className="font-semibold transition-all duration-300 ease-in-out hover:text-indigo-600"
                                                >
                                                    {item.nftName}
                                                </Link>
                                                <p className="my-2 text-base text-[#7A7474]">
                                                    {item.property.city},{' '}
                                                    {item.property.state}
                                                </p>
                                                <h6 className="font-semibold">
                                                    {(
                                                        item.price / tokenRate
                                                    ).toFixed(2)}{' '}
                                                    ETH
                                                </h6>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        </div>
                    </div>
                    {/* Small Images */}
                    <div className="flex flex-col gap-4 sm:gap-10 md:hidden">
                        <div className="w-full">
                            <div
                                onClick={() => setIsModalOpen(product)}
                                className="relative cursor-pointer"
                            >
                                {/* Image */}
                                <Image
                                    src={product?.otherImages[1]}
                                    width={700}
                                    height={600}
                                    className="h-auto w-full rounded-lg object-cover opacity-55"
                                    alt="Feature Hero Side 1"
                                />

                                {/* Black Overlay */}
                                <div className="absolute inset-0 bg-black bg-opacity-55"></div>

                                {/* Centered Text Overlay */}
                                <div className="absolute inset-0 z-[1] flex items-center justify-center">
                                    <div className="flex flex-col items-center text-center text-lg font-semibold text-white">
                                        <CiCamera size={50} />
                                        <h4 className="text-2xl">
                                            Show all
                                        </h4>{' '}
                                        <p className="text-base">
                                            {product?.otherImages?.length}{' '}
                                            photos
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="w-full">
                            <Image
                                src="/images/features/features-Hero-Image02.png"
                                width={700}
                                height={600}
                                className="h-auto w-full rounded-lg object-cover"
                                alt="Feature Hero Side 2"
                            />
                        </div>
                    </div>
                    <div className="mt-7 flex justify-center lg:hidden">
                        <SolidButton className="h-[45px] w-3/4 rounded-md border border-primary bg-tertiary transition-colors duration-300 hover:border-tertiary hover:bg-primary">
                            <p className="text-sm text-primary hover:text-tertiary">
                                Place a Bid
                            </p>
                        </SolidButton>
                    </div>
                </div>
            </div>
            {isModalOpen && (
                <ImageGalleryModal
                    isModalOpen={isModalOpen}
                    setIsModalOpen={setIsModalOpen}
                />
            )}
        </>
    );
}

export default FeaturesDetails;
