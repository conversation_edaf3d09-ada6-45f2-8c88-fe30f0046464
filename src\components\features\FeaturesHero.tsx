import { TNft } from '@/server/model/product/productType';

function FeaturesHero({ product }: { product: TNft }) {
    const usdPrice = product?.price || 0;
    const tokenRate = 1636.73;
    const ethPrice = (usdPrice / tokenRate)?.toFixed(2);
    const highestBid = product?.bids?.length
        ? Math.max(
              ...product?.bids?.map((b: any) => parseFloat(b.amount || '0'))
          )
        : 0;
    return (
        <section className="bg-gradient-to-t from-white via-white to-[#F5F5F5]">
            <div className="mx-auto max-w-screen-xl px-6 pt-[90px] sm:px-0 sm:pt-[100px]">
                <h4 className="font-poppins my-10 text-center text-4xl tracking-[-0.05em] text-secondary md:my-[65px] md:text-start md:text-[50px] md:font-[800]">
                    Property Features
                </h4>
                <div className="flex flex-row items-center justify-between gap-6 px-0 sm:gap-0 md:px-4">
                    {/* Left Side */}
                    <div className="text-center sm:text-left">
                        <h1 className="font-sans text-xl font-bold leading-[1.2] tracking-[-0.04em] text-secondary sm:text-[50px] md:text-3xl">
                            {product?.nftName}
                        </h1>
                        <p className="font-sans text-lg font-normal leading-[1.2] tracking-[-0.04em] text-secondary sm:text-[24px]">
                            {product?.property?.city},{' '}
                            {product?.property?.state}
                        </p>
                    </div>

                    {/* Right Side */}
                    <div className="text-center sm:text-right">
                        <h2 className="font-sans text-xl font-bold leading-[1.2] tracking-[-0.04em] text-accent_blue sm:text-[50px] md:text-3xl">
                            {ethPrice || 0} ETH / ${' '}
                            {product?.price?.toLocaleString() || 0}
                        </h2>
                        <p className="text-lg font-normal leading-[1.2] tracking-[-0.04em] text-secondary sm:text-[24px]">
                            Highest bid:
                            <span className="font-semibold">
                                {' '}
                                {highestBid || 0} ETH
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </section>
    );
}

export default FeaturesHero;
