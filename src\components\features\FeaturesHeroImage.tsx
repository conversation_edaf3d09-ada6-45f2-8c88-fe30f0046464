'use client';

import { TNft } from '@/server/model/product/productType';
import Image from 'next/image';
import { useState } from 'react';
import { CiCamera } from 'react-icons/ci';
import ImageGalleryModal from './ImageGalleryModal';
import ShareModal from './ShareModal';

function FeaturesHeroImage({ product }: { product: TNft }) {
    const [isModalOpen, setIsModalOpen] = useState<TNft | null>(null);
    const [isShareModalOpen, setIsShareModalOpen] = useState<TNft | null>(null);

    return (
        <>
            <div className="mx-auto mt-10 max-w-screen-xl px-4 sm:mt-20">
                <div className="grid grid-cols-1 items-start gap-4 sm:grid-cols-2 sm:gap-7 lg:grid-cols-3">
                    {/* Large Image */}
                    <div className="col-span-1 sm:col-span-2">
                        <Image
                            src={product?.otherImages[0]}
                            width={1200}
                            height={1000}
                            placeholder="blur"
                            blurDataURL="/images/features/blurred.png"
                            className="h-[100%] w-full rounded-lg object-cover"
                            alt="Feature Hero Main"
                        />
                    </div>

                    {/* Small Images */}

                    <div className="hidden gap-5 md:flex lg:flex-col">
                        {/* show All Image  */}
                        <div className="w-1/2 lg:w-full">
                            <div
                                onClick={() => setIsModalOpen(product)}
                                className="relative cursor-pointer"
                            >
                                {/* Image */}
                                <Image
                                    src={product?.otherImages[0]}
                                    width={700}
                                    height={600}
                                    placeholder="blur"
                                    blurDataURL="/images/features/blurred.png"
                                    className="h-auto w-full rounded-lg object-cover opacity-55"
                                    alt="Feature Hero Side 1"
                                />

                                {/* Black Overlay */}
                                <div className="absolute inset-0 bg-black/50"></div>

                                {/* Centered Text Overlay */}
                                <div className="absolute inset-0 z-[1] flex items-center justify-center">
                                    <div className="flex flex-col items-center text-center text-lg font-semibold text-white">
                                        <CiCamera size={50} />
                                        <h4 className="text-2xl">
                                            Show all
                                        </h4>{' '}
                                        <p className="text-base">
                                            {product?.otherImages.length || 0}{' '}
                                            photos
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="w-1/2 lg:w-full">
                            <Image
                                src="/images/features/features-Hero-Image02.png"
                                width={700}
                                height={600}
                                placeholder="blur"
                                blurDataURL="/images/features/blurred.png"
                                className="h-auto w-full rounded-lg object-cover"
                                alt="Feature Hero Side 2"
                            />
                        </div>
                    </div>
                </div>
            </div>
            {/* Image Modal */}
            <ImageGalleryModal
                isModalOpen={isModalOpen}
                setIsModalOpen={setIsModalOpen}
                setIsShareModalOpen={setIsShareModalOpen}
            />
            {/* Share Modal */}
            <ShareModal
                isShareModalOpen={isShareModalOpen}
                setIsShareModalOpen={setIsShareModalOpen}
            />
        </>
    );
}

export default FeaturesHeroImage;
