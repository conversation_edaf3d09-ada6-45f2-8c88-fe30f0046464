'use client';

import { TNft } from '@/server/model/product/productType';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { AiOutlineClose } from 'react-icons/ai';
import { FaRegHeart } from 'react-icons/fa';
import { RiShareForwardFill } from 'react-icons/ri';

interface ImageGalleryModalProps {
    isModalOpen: TNft | null;
    setIsModalOpen: (isOpen: TNft | null) => void;
    setIsShareModalOpen?: any;
}

const ImageGalleryModal = ({
    isModalOpen,
    setIsModalOpen,
    setIsShareModalOpen
}: ImageGalleryModalProps) => {
    const [animateIn, setAnimateIn] = useState(false);
    const images = isModalOpen?.otherImages;

    useEffect(() => {
        if (isModalOpen) {
            document.body.classList.add('modal-open');
            setAnimateIn(true);
        } else {
            document.body.classList.remove('modal-open');
            setAnimateIn(false);
        }

        return () => {
            document.body.classList.remove('modal-open');
        };
    }, [isModalOpen]);

    if (!images) return null;

    return createPortal(
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#7A7474] bg-opacity-85 px-4 backdrop-blur-sm">
            <div
                className={`scrollbar-hidden relative max-h-[95vh] w-full max-w-6xl transform rounded-xl bg-white p-4 shadow-2xl transition-all duration-700 ease-out ${
                    animateIn ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
                }`}
            >
                <div className="scrollbar-hidden max-h-[85vh] overflow-y-auto rounded-lg">
                    <div className="mb-4 mt-3 flex items-center justify-between px-3 md:px-5">
                        <div>
                            <button
                                className="text-gray-700 transition-all duration-500 ease-in-out hover:rotate-[90deg] hover:text-red-500"
                                onClick={() => {
                                    setAnimateIn(false);
                                    setTimeout(() => setIsModalOpen(null), 300);
                                }}
                            >
                                <AiOutlineClose size={28} />
                            </button>
                        </div>
                        <div className="flex items-center gap-6 text-gray-500">
                            <button
                                className="flex items-center gap-2 text-gray-500 transition-all duration-300 ease-in-out hover:text-black"
                                onClick={() => {
                                    setAnimateIn(false);
                                    setTimeout(() => {
                                        setIsShareModalOpen(isModalOpen),
                                            setIsModalOpen(null);
                                    }, 300);
                                }}
                            >
                                Share listing <RiShareForwardFill size={28} />
                            </button>
                            <button
                                className="flex items-center gap-2 text-gray-500 transition-all duration-300 ease-in-out hover:text-black"
                                // onClick={() => {
                                //     setAnimateIn(false);
                                //     setTimeout(() => setIsModalOpen(null), 300);
                                // }}
                            >
                                Favorite <FaRegHeart size={24} />
                            </button>
                        </div>
                    </div>
                    <div className="mb-4 px-3 text-center sm:text-left md:px-5">
                        <h1 className="font-sans text-xl font-bold leading-[1.2] tracking-[-0.04em] text-secondary sm:text-[50px] md:text-3xl">
                            {isModalOpen?.nftName}
                        </h1>
                        <p className="font-sans text-lg font-normal leading-[1.2] tracking-[-0.04em] text-secondary sm:text-[24px]">
                            {isModalOpen?.property?.city},{' '}
                            {isModalOpen?.property?.state}
                        </p>
                    </div>
                    <div className="mb-4 grid grid-cols-1 gap-4 px-3 md:grid-cols-2 md:px-5 lg:grid-cols-2">
                        {images.map((image, index) => (
                            <div key={index} className="">
                                <Image
                                    width={500}
                                    height={500}
                                    src={image}
                                    alt={`Gallery Image ${index + 1}`}
                                    className="h-full w-full overflow-hidden rounded-lg object-cover"
                                />
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>,
        document.body
    );
};

export default ImageGalleryModal;
