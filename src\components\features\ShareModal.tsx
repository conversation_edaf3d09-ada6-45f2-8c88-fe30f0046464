'use client';

import { TNft } from '@/server/model/product/productType';
import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { useFieldArray, useForm } from 'react-hook-form';
import { AiOutlineClose } from 'react-icons/ai';
import { FaPlus, FaRegEnvelope } from 'react-icons/fa';
import { HiMiniLink } from 'react-icons/hi2';
import { toast } from 'sonner';

interface ShareModalProps {
    isShareModalOpen: TNft | null;
    setIsShareModalOpen: any;
}

interface FormValues {
    emails: { address: string }[];
    message: string;
}

const ShareModal = ({
    isShareModalOpen,
    setIsShareModalOpen
}: ShareModalProps) => {
    const [animateIn, setAnimateIn] = useState(false);

    const { register, control, handleSubmit, watch, reset } =
        useForm<FormValues>({
            defaultValues: {
                emails: [{ address: '' }],
                message: ''
            }
        });

    const { fields, append, remove } = useFieldArray({
        control,
        name: 'emails'
    });

    const onSubmit = (data: FormValues) => {
        console.log('Form Data:', data);
        // handle actual share logic here
        reset();
        toast.success('Listing shared successfully!');
    };

    useEffect(() => {
        if (isShareModalOpen) {
            document.body.classList.add('modal-open');
            setAnimateIn(true);
        } else {
            document.body.classList.remove('modal-open');
            setAnimateIn(false);
        }

        return () => {
            document.body.classList.remove('modal-open');
        };
    }, [isShareModalOpen]);

    if (!isShareModalOpen) return null;

    return createPortal(
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#7A7474] bg-opacity-85 px-4 backdrop-blur-sm">
            <div
                className={`scrollbar-hidden relative max-h-[95vh] w-full max-w-4xl transform rounded-xl bg-white p-4 shadow-2xl transition-all duration-700 ease-out ${
                    animateIn ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
                }`}
            >
                <div className="">
                    <div className="mb-7 mt-4 flex items-center justify-between px-3 md:px-5">
                        <h2 className="text-start font-changa text-3xl">
                            Share Listing
                        </h2>
                        <button
                            className="text-gray-700 transition-all duration-500 ease-in-out hover:rotate-[90deg] hover:text-red-500"
                            onClick={() => {
                                setAnimateIn(false);
                                setTimeout(
                                    () => setIsShareModalOpen(null),
                                    300
                                );
                            }}
                        >
                            <AiOutlineClose size={28} />
                        </button>
                    </div>

                    <div className="mb-4 px-3 text-center sm:text-left md:px-5">
                        <h1 className="font-sans text-xl font-bold text-secondary sm:text-[50px] md:text-2xl">
                            {isShareModalOpen?.nftName}
                        </h1>
                        <p className="font-sans text-lg text-secondary sm:text-[18px]">
                            {isShareModalOpen?.property?.city},{' '}
                            {isShareModalOpen?.property?.state}
                        </p>
                    </div>

                    <form
                        onSubmit={handleSubmit(onSubmit)}
                        className="mb-4 px-3 text-center sm:text-left md:px-5"
                    >
                        {fields.map((field, index) => (
                            <div
                                className="mb-4 flex items-center justify-between gap-2"
                                key={field.id}
                            >
                                <div className="w-full">
                                    <label>Email Address</label>
                                    <input
                                        {...register(
                                            `emails.${index}.address` as const
                                        )}
                                        className="h-12 w-full border px-3 outline-0 focus:outline-1 focus:outline-gray-300"
                                        type="email"
                                        placeholder="Enter email address"
                                    />
                                </div>

                                <div className="mt-6 flex items-center gap-2">
                                    {fields.length > 1 && (
                                        <button
                                            type="button"
                                            onClick={() => remove(index)}
                                            className="flex h-10 w-10 items-center justify-center rounded-full text-red-500 transition-all duration-500 ease-in-out hover:rotate-[90deg]"
                                            title="Remove Email"
                                        >
                                            <AiOutlineClose size={18} />
                                        </button>
                                    )}

                                    {index === fields.length - 1 && (
                                        <button
                                            type="button"
                                            onClick={() =>
                                                append({ address: '' })
                                            }
                                            className="flex h-10 w-10 items-center justify-center rounded-full text-black transition-all duration-300 ease-in-out hover:text-accent_blue"
                                            title="Add Email"
                                        >
                                            <FaPlus size={18} />
                                        </button>
                                    )}
                                </div>
                            </div>
                        ))}

                        <div className="mt-8">
                            <label>Send a Message</label>
                            <textarea
                                {...register('message')}
                                className="h-32 w-full border px-3 py-2 outline-0 focus:outline-1 focus:outline-gray-300"
                                placeholder="Enter message"
                                maxLength={250}
                            ></textarea>
                            <span className="text-base text-[#7A7474]">
                                {watch('message')?.length || 0}/250 Character
                                limit
                            </span>
                        </div>

                        <div className="my-10 flex justify-center">
                            <button
                                type="submit"
                                className="flex items-center gap-2 rounded bg-blue-500 px-14 py-2 text-white hover:bg-blue-600"
                            >
                                Share Listing
                            </button>
                        </div>
                    </form>

                    <div className="mb-4 px-3 md:px-5">
                        <div className="flex items-center justify-center gap-14 text-gray-500">
                            <HiMiniLink
                                size={30}
                                className="cursor-pointer transition-all duration-300 hover:text-accent_blue"
                            />
                            <FaRegEnvelope
                                size={28}
                                className="cursor-pointer transition-all duration-300 hover:text-accent_blue"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>,
        document.body
    );
};

export default ShareModal;
