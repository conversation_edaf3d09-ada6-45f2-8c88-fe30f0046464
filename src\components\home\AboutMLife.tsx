function AboutMLife() {
    return (
        <>
            {' '}
            <div className="min-h-screen bg-black">
                <section
                    style={{
                        backgroundImage:
                            "url('/images/landing/about-MLife-BG-img.png')",
                        //backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        backgroundSize: '110% 100%'
                    }}
                    className="w-full bg-black bg-auto bg-no-repeat text-white"
                >
                    {/* Background Image */}
                    {/* <div className="inset-0 -right-28 z-0 h-full w-full bg-no-repeat" /> */}

                    {/* Content Container */}
                    <div className="mx-auto max-w-screen-xl px-4 py-16 md:py-24">
                        {/* About $MLife Header */}
                        <div className="mb-12 mt-44 text-end">
                            <h2 className="font-poppins mb-4 text-5xl font-bold md:text-[97px] md:font-[700] md:leading-[94px] md:tracking-[-0.05em]">
                                About $MLife
                            </h2>
                            <p className="text-sm opacity-80 md:mt-9 md:text-lg">
                                $MLife is the core utility token enabling{' '}
                                <br className="block md:hidden" />
                                participants to access homeownership{' '}
                                <br className="block md:hidden" />
                                opportunities more{' '}
                                <br className="hidden md:block" /> efficiently,
                                participate <br className="block md:hidden" />{' '}
                                in governance, and enjoy platform{' '}
                                <br className="block md:hidden" /> benefits.
                            </p>
                        </div>

                        {/* What $MLife Enables Section */}
                        <div className="mt-16 md:mt-44">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
                                {/* Left column with heading */}
                                <div className="flex flex-col justify-center rounded-xl border border-white/10 bg-[#FFFFFF]/15 px-8 py-16 backdrop-blur-[70px] md:col-span-2 md:py-28">
                                    <h3 className="font-poppins text-center text-[50px] font-[700] leading-[42px] tracking-[-0.05em] md:text-[80px] md:leading-[62px] md:tracking-[-0.05em]">
                                        What
                                        <br />
                                        $MLIFE
                                        <br />
                                        Enables
                                    </h3>
                                </div>

                                {/* Right columns with 2x2 grid */}
                                <div className="grid grid-cols-1 gap-4 md:col-span-3 md:grid-cols-2">
                                    <div className="flex items-center justify-center rounded-lg border border-white/10 bg-white/10 px-6 py-8 backdrop-blur-[70px] md:py-6">
                                        <p className="text-center text-lg leading-[20px] opacity-80 md:tracking-[-0.04em]">
                                            Access to <br /> curated real <br />{' '}
                                            estate programs <br /> and benefits
                                        </p>
                                    </div>
                                    <div className="flex items-center justify-center rounded-lg border border-white/10 bg-white/15 px-6 py-8 backdrop-blur-[70px] md:py-6">
                                        <p className="text-center text-lg leading-[20px] opacity-80 md:tracking-[-0.04em]">
                                            Voting rights on <br /> key platform{' '}
                                            <br /> governance <br /> matters
                                        </p>
                                    </div>
                                    <div className="col-span-2">
                                        <div className="flex h-full items-center justify-center gap-5 rounded-lg border border-white/10 bg-white/15 py-10 backdrop-blur-[70px]">
                                            <p className="w-full border-r-2 py-7 pe-4 text-end text-lg leading-[20px] opacity-80 md:px-5 md:tracking-[-0.04em]">
                                                Fee discounts{' '}
                                                <br className="md:hidden" /> and{' '}
                                                <br className="hidden md:block" />{' '}
                                                participation <br />
                                                incentives
                                            </p>
                                            <p className="w-full text-start text-lg leading-[20px] opacity-80 md:tracking-[-0.04em]">
                                                Aligned ecosystem <br /> growth
                                                and shared <br /> community
                                                value
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Token Structure Section */}
                        <div className="">
                            <h3 className="font-poppins my-20 text-5xl font-extrabold leading-10 tracking-[-0.05em] text-white md:text-[80px] md:font-normal md:leading-[62px] lg:font-[700]">
                                Token
                                <br />
                                Structure
                            </h3>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                <div className="rounded-xl border border-white/10 bg-[linear-gradient(139.71deg,_rgba(245,_0,_212,_0.5)_-2.93%,_rgba(143,_0,_124,_0.5)_104.16%)] py-16 backdrop-blur-[80px]">
                                    <p className="mb-2 pb-3 text-center text-lg md:pb-0">
                                        Capped supply:
                                    </p>
                                    <h4 className="font-poppins text-center text-6xl font-[600] leading-4 tracking-[-0.07em] text-white md:text-[70px] md:leading-[124px] md:tracking-[-0.05em] lg:font-[700]">
                                        2B tokens
                                    </h4>
                                </div>
                                <div className="hidden w-full items-center justify-center rounded-lg border border-white/10 bg-[linear-gradient(226.97deg,_rgba(210,_9,_183,_0.4)_25.94%,_rgba(13,_6,_12,_0.4)_115.37%)] p-6 backdrop-blur-md md:flex">
                                    <p className="mb-2 text-center text-lg opacity-80 md:leading-[20px] md:tracking-[-0.04em]">
                                        Burn mechanics tied to{' '}
                                        <br className="hidden md:block" />{' '}
                                        protocol revenue
                                    </p>
                                </div>
                                <div className="hidden w-full items-center justify-center rounded-lg border border-white/10 bg-[linear-gradient(139.71deg,_rgba(245,_0,_212,_0.4)_-2.93%,_rgba(143,_0,_124,_0.4)_104.16%)] p-6 backdrop-blur-md md:flex">
                                    <p className="mb-2 text-center text-lg opacity-80 md:leading-[20px] md:tracking-[-0.04em]">
                                        Integrated with staking, access <br />{' '}
                                        control, and governance features
                                    </p>
                                </div>
                                <div className="flex flex-row gap-4 md:hidden">
                                    <div className="flex w-full items-center justify-center rounded-lg border border-white/10 bg-[linear-gradient(226.97deg,_rgba(210,_9,_183,_0.4)_25.94%,_rgba(13,_6,_12,_0.4)_115.37%)] p-6 backdrop-blur-md">
                                        <p className="mb-2 text-center text-sm opacity-80">
                                            Burn mechanics tied to{' '}
                                            <br className="hidden md:block" />{' '}
                                            protocol revenue
                                        </p>
                                    </div>
                                    <div className="flex w-full items-center justify-center rounded-lg border border-white/10 bg-[linear-gradient(139.71deg,_rgba(245,_0,_212,_0.4)_-2.93%,_rgba(143,_0,_124,_0.4)_104.16%)] p-6 backdrop-blur-md">
                                        <p className="mb-2 text-center text-sm opacity-80">
                                            Integrated with staking, access
                                            <br className="hidden md:block" />{' '}
                                            control, and governance features
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                {/* Bottom Tagline */}
                <div className="mx-auto max-w-screen-xl px-4 py-16 md:px-0">
                    <h3 className="font-poppins py-20 text-4xl font-[600] leading-9 tracking-[-0.02em] text-white md:text-[61px] md:leading-[54px] lg:font-[700]">
                        $MLIFE isn't just a token. <br className="block" /> It's
                        a movement to bridge <br className="hidden md:block" />{' '}
                        more families to their future{' '}
                        <br className="hidden md:block" /> homes.
                    </h3>
                </div>
            </div>
        </>
    );
}

export default AboutMLife;
