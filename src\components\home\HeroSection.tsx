// components/sections/HeroSection.tsx
'use client';
import Image from 'next/image';
import SolidButton from '../button/SolidButton'; 
import HomeHeader from '../common/HomeHeader'; 
import Waitlist from '../home/<USER>'; // <<<< Ensure this path is correct
import { useState } from 'react';

export default function HeroSection() {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const openModal = () => {
        // console.log('HeroSection: openModal called');
        setIsModalOpen(true);
    };
    const closeModal = () => {
        // console.log('HeroSection: closeModal called');
        setIsModalOpen(false);
    };

    return (
        <>
            {/* Desktop Section */}
            <section className="relative hidden h-[60vh] w-full overflow-hidden bg-black md:block lg:h-[906px]">
                {/* Background Image */}
                <div
                    className="absolute bottom-[-14rem] left-0 right-0 top-0 h-full w-full -rotate-[9deg] transform-none bg-cover bg-center bg-no-repeat"
                    style={{
                        backgroundImage: `url('/images/landing/ML-Hero-BG.png')`
                    }}
                />
				
                {/* Header */}
                <div className="relative z-10 w-full lg:top-10">
                    <HomeHeader />
                </div>
                
                {/* Hero Content */}
                <div className="absolute inset-0 mx-auto -mt-16 flex flex-col items-center justify-center px-4 text-center">
                    <h1 className="typewriter font-poppins flex w-full flex-col text-center text-4xl leading-4 tracking-[-0.04em] text-white md:text-[97px] md:leading-[94px] lg:font-[900]">
                        Real-World Ownership, 
						<br className="md:block hidden" />
						<span className="typewriter">
						Reinvented
						</span>
                    </h1>

                    <div className="w-full pb-4 pt-12 text-center text-lg font-light text-white">
                        <p>
                            Own real assets, earn real returns, and govern your
                            portfolio from anywhere.
                        </p>
                    </div>

                    <div className="flex flex-col gap-6 md:flex-row md:gap-10"> 
                        <SolidButton
                            href="/whitepaper"
                            className="mt-6 flex h-[50px] w-[210px] items-center justify-center rounded-full border border-[#05AAFF] bg-[#05AAFF] text-white transition-all duration-300 hover:shadow-[0_0_10px_white]"
                        >
                            <span className="text-sm md:text-base">
                                Download Whitepaper
                            </span>
                        </SolidButton>
                        <SolidButton
                            onClick={() => {
                                // console.log('Desktop: Join the Waitlist button clicked!');
                                openModal();
                            }}
                            className="mt-6 flex h-[50px] w-[210px] items-center justify-center rounded-full border border-tertiary bg-primary text-tertiary shadow-[0_0_15px_#62C5FF] transition-all duration-300 hover:border-white"
                        >
                            <p className="text-sm md:text-base">
                                Join the Waitlist
                            </p>
                        </SolidButton>
                    </div>
                </div>
            </section>

            {/* Mobile Section */}
            <section className="md:hidden"> 
                <div className="relative h-screen w-full overflow-hidden"> 
                    <div className="relative z-20 w-full border-b border-black bg-black text-white md:border-none"> 
                        <HomeHeader />
                    </div>
                    <div className="absolute inset-0 z-0 h-full w-full bg-black"> 
                        <Image
                            src="/images/landing/ML-Hero-BG.png"
                            fill
                            style={{ objectFit: 'cover', opacity: 0.7 }} 
                            alt="Banner"
                            priority
                        />
                    </div>
                    <div className="relative z-10 mx-auto mt-[15vh] flex max-w-[960px] flex-col items-center justify-start px-4 text-center text-white"> 
                        <h1 className="typewriter font-poppins w-full text-center text-[44px] font-black leading-tight tracking-[-0.05em] sm:text-[50px]"> 
                            Real-World <br /> Ownership, <br /> Reinvented
                        </h1>

                        <div className="w-full px-2 pt-6 text-center text-sm text-gray-200 sm:text-base"> 
                            <p>
                                MLife bridges institutional-grade real estate
                                with the accessibility and programmability of
                                blockchain. Own real assets, earn real returns,
                                and govern your portfolio from anywhere.
                            </p>
                        </div>

                        <div className="space-y-6 pt-12 sm:pt-16"> 
                            <SolidButton
                                href="/whitepaper"
                                className="flex h-[50px] w-[210px] items-center justify-center rounded-full border border-[#05AAFF] bg-[#05AAFF] text-white transition-all duration-300 hover:shadow-[0_0_10px_white]"
                            >
                                <span className="text-sm md:text-base">
                                    Download Whitepaper
                                </span>
                            </SolidButton>
                            <SolidButton
                                onClick={() => {
                                    // console.log('Mobile: Join the Waitlist button clicked!');
                                    openModal();
                                }}
                                className="flex h-[50px] w-[210px] items-center justify-center rounded-full border border-tertiary bg-primary text-tertiary shadow-[0_0_15px_#62C5FF] transition-all duration-300 hover:border-white"
                            >
                                <span className="text-sm md:text-base">
                                    Join the Waitlist
                                </span>
                            </SolidButton>
                        </div>
                    </div>
                </div>
            </section>

            {/* Render the Modal */}
            <Waitlist isOpen={isModalOpen} onClose={closeModal} />
        </>
    );
}