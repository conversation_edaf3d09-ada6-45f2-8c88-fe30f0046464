function HowItWorks() {
    const cardData = [
        {
            number: '01.',
            text: 'Browse carefully selected property opportunities'
        },
        {
            number: '02.',
            text: 'Secure access using fiat, stablecoins, or $MLIFE'
        },
        {
            number: '03.',
            text: 'Track your homeownership progress transparently through the platform'
        },
        {
            number: '04.',
            text: 'Participate in governance around platform initiatives and community development'
        },
        {
            number: '05.',
            text: 'Access liquidity solutions if and when needed'
        }
    ];
    return (
        <>
            <div className="mx-auto max-w-[1700px]">
                <section
                    className="mx-auto hidden h-full bg-right-top bg-no-repeat md:block"
                    style={{
                        backgroundImage:
                            "url('/images/landing/how-it-works-BG.png')"
                    }}
                >
                    <div className="mx-auto max-w-screen-3xl px-4 pt-[120px] md:pt-[240px]">
                        <h4 className="font-poppins text-start text-4xl leading-4 tracking-[-0.05em] text-black md:text-[97px] md:font-[700] md:leading-[94px]">
                            How It Works
                        </h4>
                        <p className="font-poppins mt-5 tracking-[-0.04em]">
                            Built for capital efficiency, transparent tracking,{' '}
                            <br /> and global accessibility.
                        </p>
                    </div>
                    <div className="pb-10">
                        <div className="my-36 bg-[#FFFAFA4A]/30 px-4 py-10 backdrop-blur-lg">
                            <div className="grid grid-cols-1 gap-3 md:grid-cols-5 md:ps-8">
                                {cardData.map((item, index) => {
                                    const [firstWord, ...restWords] =
                                        item.text.split(' ');
                                    const restText = restWords.join(' ');

                                    return (
                                        <div
                                            key={index}
                                            className="flex items-center justify-center gap-4"
                                        >
                                            <h4 className="font-poppins border-r-2 border-black px-2 py-5 text-end text-[63px] font-[700] leading-[124px] tracking-[0%] text-black">
                                                {item.number}
                                            </h4>
                                            <div className="pe-7 text-center text-black">
                                                <p>
                                                    <span className="font-bold">
                                                        {firstWord}
                                                    </span>{' '}
                                                    {restText}
                                                </p>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                </section>
            </div>
            <section
                className="h-full w-full bg-left-bottom bg-no-repeat md:hidden"
                style={{
                    backgroundImage:
                        "url('/images/landing/how-it-works-mobile-BG.png')"
                }}
            >
                <div className="mx-auto max-w-screen-xl px-4 pt-[120px] md:pt-[240px]">
                    <h4 className="font-poppins text-center text-5xl font-extrabold leading-none tracking-[-0.04em] text-black md:text-start md:text-[97px] md:font-normal md:leading-[94px] lg:font-[900]">
                        How It Works
                    </h4>
                    <p className="font-poppins my-9 text-center tracking-[-0.04em] md:mt-2 md:text-start">
                        Built for capital efficiency, transparent tracking,{' '}
                        <br /> and global accessibility.
                    </p>
                </div>
                <div className="pb-10">
                    <div className="mx-auto my-5 max-w-screen-4xl px-4 py-10 md:my-36">
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-5 md:gap-3">
                            {cardData.map((item, index) => (
                                <div
                                    key={index}
                                    className="flex items-center justify-center gap-4 rounded-2xl border border-white/30 bg-white/30 px-8 py-6 shadow-lg backdrop-blur-md md:px-14"
                                >
                                    <h4 className="font-poppins border-r-2 border-black px-2 text-end text-[80px] font-[700] leading-[124px] tracking-[0%] text-black md:w-2/5">
                                        {item.number}
                                    </h4>
                                    <div className="text-start text-black md:w-3/5">
                                        <p className="leading-[20px] md:w-[260px]">
                                            {item.text}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>
        </>
    );
}

export default HowItWorks;
