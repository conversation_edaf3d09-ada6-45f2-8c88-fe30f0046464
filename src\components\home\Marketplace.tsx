'use client';
import { TNft } from '@/server/model/product/productType';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { BiBath } from 'react-icons/bi';
import { MdOutlineBed } from 'react-icons/md';
import SolidButton from '../button/SolidButton';

export default function Marketplace({ products }: { products: TNft[] }) {
    const [active, setActive] = useState<'rent' | 'buy'>('buy');
    const router = useRouter();
    const buttons = [
        {
            key: 'buy',
            label: 'Buy',
            iconBlack: '/icons/buy-icon.svg',
            iconBlue: '/icons/buy-blue-icon.svg'
        },
        {
            key: 'rent',
            label: 'Rent',
            iconBlack: '/icons/key-icon.svg',
            iconBlue: '/icons/key-blue-icon.svg'
        }
    ] as const;
    return (
        <section className="bg-gradient-to-t from-white via-white to-[#F5F5F5]">
            <div className="mx-auto max-w-screen-xl py-16 lg:pt-[240px]">
                <h2 className="text-center font-changa text-4xl text-secondary md:text-[65px]">
                    Marketplace
                </h2>
                {/*  <div className="flex flex-col items-center justify-between pb-[70px] pt-7 md:pt-[78px] lg:flex-row">
                    <div className="flex w-fit items-center gap-2 border-2 border-[#E9E9E9] bg-[#F9F9F9] p-3">
                        {buttons.map((btn) => {
                            const isActive = active === btn.key;

                            return (
                                <SolidButton
                                    key={btn.key}
                                    onClick={() => {
                                        setActive(btn.key);
                                        router.push(`?type=${btn.key}`, {
                                            scroll: false
                                        });
                                    }}
                                    className={`flex h-[48px] w-[115px] items-center justify-center gap-x-2 rounded-md border-2 transition-all duration-300 ease-in-out ${
                                        isActive
                                            ? 'border-[#EBEDF0] bg-primary'
                                            : 'border-transparent bg-transparent'
                                    }`}
                                >
                                    <Image
                                        src={
                                            isActive
                                                ? btn.iconBlue
                                                : btn.iconBlack
                                        }
                                        width={20}
                                        height={20}
                                        className={`transition-all duration-300 ease-in-out ${
                                            isActive ? 'h-7 w-7' : 'h-5 w-5'
                                        }`}
                                        alt={btn.label}
                                    />
                                    <p
                                        className={`text-lg font-medium transition-colors duration-300 ease-in-out ${
                                            isActive
                                                ? 'text-accent_blue'
                                                : 'text-gray-500'
                                        }`}
                                    >
                                        {btn.label}
                                    </p>
                                </SolidButton>
                            );
                        })}
                    </div>
                     <div className="mx-auto my-3 mt-5 h-[57px] w-[380px] rounded-md border-2 border-[#EBEDF0] bg-[#F5F5F5] lg:mx-0 lg:mt-0 lg:h-[64px] lg:w-[352px] lg:shadow-none">
                        <div className="flex h-full items-center gap-x-3 px-4">
                            <button className="h-6 w-6">
                                <FiSearch className="h-full w-full text-black" />
                            </button>
                            <input
                                type="text"
                                placeholder="Search..."
                                className="h-full w-full bg-inherit text-base font-medium text-gray-800 placeholder-gray-500 outline-none"
                            />
                        </div>
                    </div> 
                </div>*/}
                {/* cards */}
                <div className="mt-7 grid gap-x-12 gap-y-8 p-10 md:mt-[78px] lg:grid-cols-3 lg:p-0">
                    {products
                        ?.slice(0, 6)
                        .map((product) => (
                            <MarketplaceCard
                                key={product._id}
                                property={product}
                            />
                        ))}
                </div>

                <SolidButton
                    href="/marketplace"
                    className="mx-auto mt-6 flex h-[53px] w-[203px] items-center justify-center border-2 border-tertiary bg-tertiary text-primary transition-colors duration-300 hover:bg-white hover:text-tertiary lg:mt-[70px]"
                >
                    <p className="text-sm">Browse more properties</p>
                </SolidButton>
            </div>
        </section>
    );
}

function MarketplaceCard({ property }: { property: TNft }) {
    const ETH_CONVERSION_RATE = 1636.73;
    return (
        <Link
            className="group shadow-lg lg:shadow-none"
            href={`/marketplace/${property?._id}`}
        >
            {/* Image */}
            <div className="h-[200px] overflow-hidden rounded-t-md">
                <Image
                    src={property?.otherImages[0]}
                    width={800}
                    height={900}
                    alt="property image"
                    className="h-full w-full bg-slate-200 object-fill transition-all duration-300 ease-in-out group-hover:scale-105"
                />
            </div>

            {/* Details */}
            <div className="rounded-b-md border-2 border-[#F0EFFB] px-6 py-8">
                <div className="flex items-start justify-between">
                    <div>
                        <div className="space-y-1">
                            <h3 className="text-2xl font-extrabold text-accent_blue">
                                {(
                                    property?.price / ETH_CONVERSION_RATE
                                )?.toFixed(2) || 0}{' '}
                                ETH
                            </h3>
                            <h3 className="text-2xl font-bold text-tertiary">
                                {property?.property?.propertyAddressOne}
                            </h3>
                        </div>
                        <p className="pt-2 text-base font-medium text-gray-400">
                            {property?.property?.city},{' '}
                            {property?.property?.state}
                        </p>
                    </div>

                    {/* Favorite Button */}
                    {/* <button className="inline rounded-full border p-4">
                        <FaRegHeart className="h-5 w-5" />
                       
                    </button> */}
                </div>

                {/* Divider */}
                <div className="w-full py-4">
                    <p className="h-[2px] bg-[#F0EFFB]" />
                </div>

                {/* Features */}
                <div className="flex w-full items-center justify-between">
                    <div className="flex items-center gap-x-2">
                        <MdOutlineBed className="h-5 w-5 text-accent_blue" />
                        <p className="text-sm font-medium text-navy_blue">
                            {property?.bedrooms} Beds
                        </p>
                    </div>
                    <div className="flex items-center gap-x-2">
                        <BiBath className="h-5 w-5 text-accent_blue" />
                        <p className="text-sm font-medium text-navy_blue">
                            {property?.bathrooms} Bathrooms
                        </p>
                    </div>
                    <div className="flex items-center gap-x-2">
                        <Image
                            src="/icons/squareMeters.png"
                            width={24}
                            height={24}
                            className="h-5 w-5"
                            alt="size icon"
                        />
                        <p className="text-sm font-medium text-navy_blue">
                            {property?.squareFeet} sqft
                        </p>
                    </div>
                </div>
            </div>
        </Link>
    );
}
