'use client';
import Image from 'next/image';
import { useState } from 'react'; // Re-added useState
import { FaArrowLeft, FaArrowRight, FaLinkedin } from 'react-icons/fa'; // Re-added FaArrowLeft, FaArrowRight
import { FaXTwitter } from 'react-icons/fa6';

interface TeamMember {
    id: number;
    name: string;
    role: string;
    image: string;
    linkedinUrl?: string;
    twitterUrl?: string;
}

const teamData: TeamMember[] = [
    {
        id: 1,
        name: '<PERSON>',
        role: 'CEO, Founder',
        image: '/images/team/jake.png',
        linkedinUrl: '#',
        twitterUrl: 'https://x.com/thegoodmanlife1?s=21',
    },
    {
        id: 2,
        name: '<PERSON>',
        role: 'CFO',
        image: '/images/team/jordan.png',
        linkedinUrl: 'https://www.linkedin.com/in/jtsuero/',
    },
    {
        id: 3,
        name: '<PERSON>',
        role: 'COO',
        image: '/images/team/wang.png',
        twitterUrl: 'https://x.com/JW6123',
        linkedinUrl: 'https://www.linkedin.com/in/john-wang-779103116/',
    },
    {
        id: 4,
        name: '<PERSON>',
        role: 'Business Relations',
        image: '/images/team/Lesley.png',
        linkedinUrl: 'https://www.linkedin.com/in/lesley-heinrich-*********/',
    },
    {
        id: 5,
        name: 'Kateryna Haiduk',
        role: 'Strategic Blockchain Associate',
        image: '/images/team/Kateryna.png',
        linkedinUrl: 'https://www.linkedin.com/in/kateryna-haiduk-5259b6169/',
        twitterUrl: 'https://x.com/haiduk_i',
    },
    {
        id: 6,
        name: 'Lisa Smith',
        role: 'Accounting Lead',
        image: '/images/team/lisa.png',
        linkedinUrl: 'https://www.linkedin.com/in/lisa-smith-33b40770/',
    },
    // Add more team members for testing slider
    {
        id: 7,
        name: 'Shamir Shakher',
        role: 'Principle Eng.',
        image: '/images/team/shamir.png', // Placeholder
        linkedinUrl: 'https://www.linkedin.com/in/shamirshakher/',
        twitterUrl: 'https://x.com/shamirshakher',
    },
];

const partnerLogos = [
   "/partnerslogos/bnb.png", "/partnerslogos/Alkemyst.png", "/partnerslogos/blocksquare-removebg-preview.png",
    "/partnerslogos/building-removebg-preview.png", "/partnerslogos/certik.png", "/partnerslogos/CGV-removebg-preview.png",
    "/partnerslogos/CryptoEQ-removebg-preview.png", "/partnerslogos/EP Logo.png", "/partnerslogos/hacken.png",
    "/partnerslogos/infinite allience.jpg", "/partnerslogos/Matthews.png", "/partnerslogos/Plume_Full_Color_Logo-removebg-preview.png",
    "/partnerslogos/Propy.png", "/partnerslogos/realtyx_logo-removebg-preview.png", "/partnerslogos/sota tek.jfif",
    "/partnerslogos/WEnowhere.png", "/partnerslogos/zhs_i-removebg-preview.png",
];

const PartnerLogoScroller = () => (
    <div className="relative overflow-hidden py-8 md:py-12 bg-gray-100">
        <div className="absolute inset-y-0 left-0 w-16 md:w-32 bg-gradient-to-r from-gray-100 to-transparent z-20" />
        <div className="absolute inset-y-0 right-0 w-16 md:w-32 bg-gradient-to-l from-gray-100 to-transparent z-20" />
        <div className="flex animate-scroll space-x-8 md:space-x-12">
            <div className="flex shrink-0 items-center space-x-8 md:space-x-12">
                {partnerLogos.map((src, index) => (
                    <div
                        key={`logo-orig-${index}`}
                        className="relative h-12 w-24 md:h-16 md:w-32 shrink-0 grayscale opacity-75 transition-all hover:grayscale-0 hover:opacity-100"
                    >
                        <Image src={src} layout="fill" objectFit="contain" alt={`Partner Logo ${index + 1}`} className="p-1 md:p-2" />
                    </div>
                ))}
            </div>
            <div className="flex shrink-0 items-center space-x-8 md:space-x-12" aria-hidden="true">
                {partnerLogos.map((src, index) => (
                    <div
                        key={`logo-dupe-${index}`}
                        className="relative h-12 w-24 md:h-16 md:w-32 shrink-0 grayscale opacity-75 transition-all hover:grayscale-0 hover:opacity-100"
                    >
                        <Image src={src} layout="fill" objectFit="contain" alt={`Partner Logo ${index + 1} duplicate`} className="p-1 md:p-2" />
                    </div>
                ))}
            </div>
        </div>
    </div>
);

// TeamMemberCard: Fixed width for consistent slider calculation
const TeamMemberCard = ({ member }: { member: TeamMember }) => (
    <div
        className="flex flex-col items-center text-center flex-shrink-0 w-[160px] sm:w-[180px] md:w-[230px] px-1" // Adjusted widths: mobile, sm, md
    >
        <div className="relative h-28 w-28 sm:h-32 sm:w-32 md:h-36 md:w-36 mb-3 md:mb-4">
            <Image
                src={member.image}
                layout="fill"
                objectFit="cover"
                alt={member.name}
                className="rounded-full"
            />
        </div>
        <h3 className="text-sm sm:text-base md:text-lg font-bold leading-tight tracking-tight text-gray-900 font-poppins">
            {member.name}
        </h3>
        <p className="text-xs sm:text-sm md:text-base leading-normal text-gray-600 font-sans mt-1">
            {member.role}
        </p>
        {(member.linkedinUrl || member.twitterUrl) && (
            <div className="mt-2 md:mt-3 flex items-center justify-center gap-x-2 md:gap-x-3">
                {member.linkedinUrl && member.linkedinUrl !== '#' && (
                    <a href={member.linkedinUrl} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-indigo-500 transition-colors" aria-label={`${member.name}'s LinkedIn`}>
                        <FaLinkedin size={16} md-size={18} />
                    </a>
                )}
                {member.twitterUrl && member.twitterUrl !== '#' && (
                    <a href={member.twitterUrl} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-sky-500 transition-colors" aria-label={`${member.name}'s Twitter`}>
                        <FaXTwitter size={16} md-size={18} />
                    </a>
                )}
            </div>
        )}
    </div>
);

function MeetTeam() {
    const [currentIndex, setCurrentIndex] = useState(0);
    const sectionBgClass = "bg-gray-50";

    // --- Configuration for Slider ---
    // Desktop
    const itemsPerPageDesktop = 4;
    const cardWidthDesktop = 230; // Corresponds to md:w-[230px] in TeamMemberCard
    const gapDesktop = 32;       // Corresponds to md:space-x-8 (2rem) for the wrapper of cards

    // Mobile (and small tablets)
    const itemsPerPageMobile = 2; // Number of items visible
    const cardWidthMobile = 180;  // Corresponds to sm:w-[180px] in TeamMemberCard (adjust based on preference)
    const gapMobile = 16;         // Corresponds to space-x-4 (1rem)

    // --- Event Handlers ---
    const handlePrev = () => {
        setCurrentIndex(prev => Math.max(0, prev - 1));
    };

    const handleNext = (itemsPerPage: number) => {
        // Allow sliding as long as the last `itemsPerPage` items are not fully shown
        const maxIndex = teamData.length - itemsPerPage;
        setCurrentIndex(prev => Math.min(maxIndex, prev + 1));
    };

    // --- Dynamic calculations for transform ---
    const transformValueDesktop = -currentIndex * (cardWidthDesktop + gapDesktop);
    const transformValueMobile = -currentIndex * (cardWidthMobile + gapMobile);

    // --- Button Visibility ---
    const showPrevButton = currentIndex > 0;
    const showNextButtonDesktop = teamData.length > itemsPerPageDesktop && currentIndex < teamData.length - itemsPerPageDesktop;
    const showNextButtonMobile = teamData.length > itemsPerPageMobile && currentIndex < teamData.length - itemsPerPageMobile;

    // --- Viewport Widths ---
    // These should be calculated to perfectly fit the visible items and their gaps.
    const viewportWidthDesktop = itemsPerPageDesktop * cardWidthDesktop + (itemsPerPageDesktop - 1) * gapDesktop;
    const viewportWidthMobile = itemsPerPageMobile * cardWidthMobile + (itemsPerPageMobile - 1) * gapMobile;


    return (
        <>
            {/* Desktop Section */}
            <section className={`hidden lg:block ${sectionBgClass} py-16 md:py-24`}>
                <PartnerLogoScroller />
                <div className="mx-auto max-w-screen-xl px-4 mt-16 md:mt-20">
                    <div className="text-center mb-12 md:mb-16">
                        <h2 className="font-poppins text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl md:text-5xl">
                            Meet the Team
                        </h2>
                        <p className="mt-4 max-w-2xl mx-auto text-lg leading-8 text-gray-600 font-sans">
                            Our team is committed to delivering top-notch service and innovation.
                        </p>
                    </div>

                    {teamData.length > 0 && (
                        <div className="relative flex items-center justify-center">
                            {showPrevButton && teamData.length > itemsPerPageDesktop && (
                                <button
                                    onClick={handlePrev}
                                    className="absolute left-0 top-1/2 -translate-y-1/2 z-20 p-3 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 transform -translate-x-1/2 xl:-translate-x-full" // Adjust offset
                                    aria-label="Previous team members"
                                >
                                    <FaArrowLeft size={22} className="text-gray-700" />
                                </button>
                            )}

                            <div className="overflow-hidden" style={{ width: `${viewportWidthDesktop}px` }}>
                                <div
                                    className="flex transition-transform duration-500 ease-in-out"
                                    style={{ transform: `translateX(${transformValueDesktop}px)` }}
                                >
                                    {teamData.map((member, index) => (
                                        <div
                                            key={member.id}
                                            className="shrink-0"
                                            style={{
                                                width: `${cardWidthDesktop}px`,
                                                marginRight: index < teamData.length - 1 ? `${gapDesktop}px` : '0px',
                                            }}
                                        >
                                            <TeamMemberCard member={member} />
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {showNextButtonDesktop && (
                                <button
                                    onClick={() => handleNext(itemsPerPageDesktop)}
                                    className="absolute right-0 top-1/2 -translate-y-1/2 z-20 p-3 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 transform translate-x-1/2 xl:translate-x-full" // Adjust offset
                                    aria-label="Next team members"
                                >
                                    <FaArrowRight size={22} className="text-gray-700" />
                                </button>
                            )}
                        </div>
                    )}
                </div>
            </section>

            {/* Mobile Section (includes sm and md breakpoints before lg) */}
            <section className={`lg:hidden ${sectionBgClass} py-12`}>
                <PartnerLogoScroller />
                <div className="mx-auto w-full px-4 mt-12">
                    <div className="text-center mb-8">
                        <h2 className="font-poppins text-2xl sm:text-3xl font-bold tracking-tight text-gray-900">
                            Meet the Team
                        </h2>
                        <p className="mt-3 max-w-md mx-auto text-base sm:text-md leading-7 text-gray-600 font-sans">
                            Our team is committed to delivering top-notch service and innovation.
                        </p>
                    </div>
                    {teamData.length > 0 && (
                        <div className="relative flex items-center justify-center mt-8">
                             {showPrevButton && teamData.length > itemsPerPageMobile && (
                                <button
                                    onClick={handlePrev}
                                    className="absolute left-0 top-1/2 -translate-y-1/2 z-20 p-2.5 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 transform -translate-x-1/2 sm:-translate-x-3/4" // Adjust offset
                                    aria-label="Previous team members"
                                >
                                    <FaArrowLeft size={18} className="text-gray-700" />
                                </button>
                            )}

                            <div className="overflow-hidden" style={{ width: `${viewportWidthMobile}px` }}>
                                <div
                                    className="flex transition-transform duration-500 ease-in-out"
                                    style={{ transform: `translateX(${transformValueMobile}px)` }}
                                >
                                    {teamData.map((member, index) => (
                                        <div
                                            key={member.id}
                                            className="shrink-0"
                                            style={{
                                                width: `${cardWidthMobile}px`,
                                                marginRight: index < teamData.length - 1 ? `${gapMobile}px` : '0px',
                                            }}
                                        >
                                            <TeamMemberCard member={member} />
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {showNextButtonMobile && (
                                <button
                                    onClick={() => handleNext(itemsPerPageMobile)}
                                    className="absolute right-0 top-1/2 -translate-y-1/2 z-20 p-2.5 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 transform translate-x-1/2 sm:translate-x-3/4" // Adjust offset
                                    aria-label="Next team members"
                                >
                                    <FaArrowRight size={18} className="text-gray-700" />
                                </button>
                            )}
                        </div>
                    )}
                </div>
            </section>
        </>
    );
}

export default MeetTeam;