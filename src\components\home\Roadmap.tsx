interface TimelineCardProps {
    year: string;
    phase: string;
    points: string[];
}

const timelineData: TimelineCardProps[] = [
    {
        year: '2022',
        phase: 'Phase 1',
        points: [
            'Market Research',
            'Real Estate Acquisition',
            'Smart Contract',
            'Regulatory Compliance'
        ]
    },
    {
        year: '2023',
        phase: 'Phase 2',
        points: [
            'Marketplace Development',
            'Integration of Web 3.0',
            'Main net Deployment',
            'Marketplace Listings'
        ]
    },
    {
        year: '2024',
        phase: 'Phase 3',
        points: [
            'Life Coin Development',
            'DeFi Protocol Development',
            'Launch of Life Coin',
            'Integration with DeFi'
        ]
    },
    {
        year: '2025',
        phase: 'Phase 4',
        points: [
            'Portfolio Expansion',
            'Acquisition of Homes',
            'Platform Enhancement',
            'Community Growth'
        ]
    }
];

export default function Roadmap() {
    return (
        <section className="bg-roadmap-mobile bg-cover bg-center pb-10 pt-10 lg:bg-none lg:pb-[235px] lg:pt-[153px]">
            <div className="mx-auto max-w-screen-xl">
                <h2 className="text-center font-changa text-4xl text-secondary lg:text-[65px]">
                    ManageLife Roadmap
                </h2>
                <p className="mt-3 text-center text-xl lg:mt-7">
                    See What’s Next for ManageLife.
                </p>

                <div className="grid gap-6 px-14 pt-10 lg:grid-cols-4 lg:px-0 lg:pt-[70px]">
                    {timelineData.map((data, index) => (
                        <TimelineCard key={index} {...data} />
                    ))}
                </div>
            </div>
        </section>
    );
}

function TimelineCard({ year, phase, points }: TimelineCardProps) {
    return (
        <div className="border border-[#D5D5D5] pb-[40px] pl-[30px] pt-10 shadow-lg lg:pt-[57px] lg:shadow-none">
            <div className="flex gap-x-7">
                <p className="flex flex-col font-changa text-[40px] leading-[34px] lg:font-bebas lg:text-[50px] lg:leading-[44px]">
                    {year.split('').map((char, index) => (
                        <span key={index}>{char}</span>
                    ))}
                </p>
                <div className="space-y-[5px]">
                    <h3 className="font-changa text-[32px] lg:font-bebas">
                        {phase}
                    </h3>
                    <ul className="list-outside list-disc text-base">
                        {points.map((point, index) => (
                            <li key={index}>{point}</li>
                        ))}
                    </ul>
                </div>
            </div>
        </div>
    );
}
