import Image from 'next/image';
import SolidButton from '../button/SolidButton';

interface FeatureCardProps {
    title: string;
    description: string;
}

const features = [
    {
        title: 'ManageLife Ecosystem',
        description:
            'Streamlining real estate with DeFi, $MLife, and smart contracts.'
    },
    {
        title: 'Decentralized Financing',
        description:
            'ManageLife unites DeFi, NFTs, and banking for seamless wealth management.'
    },
    {
        title: 'RWA Marketplace',
        description:
            'ManageLife RWA Marketplace turns homes into NFTs for flexible ownership.'
    }
];

export default function Solutions() {
    return (
        <section className="mx-auto max-w-screen-xl md:my-[70px]">
            <div className="justify-between lg:flex">
                <div className="bg-solutions-mobile h-[1050px] shadow-none lg:h-full lg:bg-none lg:pl-[60px]">
                    <div className="pt-10 lg:pt-20">
                        <h2 className="text-center font-changa text-4xl leading-9 text-secondary lg:text-start lg:text-[65px] lg:leading-[60px]">
                            Tokenizing <br className="block lg:hidden" /> homes{' '}
                            <br /> on chain.
                        </h2>
                        <p className="px-16 pb-10 pt-12 text-center text-[17px] leading-[27px] lg:px-0 lg:pt-2 lg:text-start">
                            Bridging the gap between digital and real-world
                            assets to make <br className="hidden lg:block" />
                            affordable housing accessible for everyone.
                        </p>
                        <div className="hidden lg:block">
                            <SolidButton
                                href="/solutions"
                                className="flex h-[45px] w-[92px] items-center justify-center border border-tertiary bg-transparent text-tertiary transition-all duration-300 hover:border-primary hover:bg-tertiary hover:text-white"
                            >
                                <span className="text-sm">Solutions</span>
                            </SolidButton>
                        </div>
                    </div>
                    <div className="grid justify-center gap-10 py-9 md:py-[70px] lg:grid-cols-1 lg:justify-start">
                        {features.map((feature, index) => (
                            <FeatureCard
                                key={index}
                                title={feature.title}
                                description={feature.description}
                            />
                        ))}
                    </div>
                    <div className="flex justify-center lg:hidden">
                        <SolidButton
                            href="/solutions"
                            className="flex h-[45px] w-[128px] items-center justify-center border border-tertiary bg-black text-white transition-all duration-300 hover:border-tertiary hover:bg-white hover:text-black"
                        >
                            <span className="text-sm">Solutions</span>
                        </SolidButton>
                    </div>
                </div>
                <div className="relative hidden h-[862px] px-4 lg:block lg:w-[432px] lg:px-0">
                    <Image
                        src="/images/landing/a.png"
                        className="h-full w-full object-fill"
                        width={1200}
                        height={1000}
                        alt=""
                    />
                    <div className="absolute left-0 right-0 top-[33.33%] flex transform flex-col gap-2">
                        <div className="h-[12px] w-full bg-white"></div>
                    </div>
                    <div className="absolute left-0 right-0 top-[66.66%] flex transform flex-col gap-2">
                        <div className="h-[12px] w-full bg-white"></div>
                    </div>
                </div>
            </div>
        </section>
    );
}

function FeatureCard({ title, description }: FeatureCardProps) {
    return (
        <div className="max-w-[310px] border bg-[#E9E9E9] bg-opacity-50 px-3 py-7 lg:border-0 lg:bg-white lg:px-0 lg:py-0">
            <h3 className="text-center text-lg font-semibold text-[#222222] lg:text-start">
                {title}
            </h3>
            <p className="text-center text-base text-gray-600 lg:text-start">
                {description}
            </p>
        </div>
    );
}
