import Image from 'next/image';

interface AllocationCardProps {
    number: string;
    title: string;
    description: string;
}

const allocationData: AllocationCardProps[] = [
    {
        number: '01',
        title: 'Seed Allocation 10%',
        description:
            "Reserved for early backers and supporters who participated in the project's initial stages."
    },
    {
        number: '02',
        title: 'Private Allocation 8%',
        description:
            "Reserved for private investors who provided essential funding and support during the project's development phase."
    },
    {
        number: '03',
        title: 'Team and Advisors 15%',
        description:
            "Allocated to reward the team and advisors for their ongoing contributions and to align their interests with the project's long-term success."
    },
    {
        number: '04',
        title: 'Our Community 67%',
        description:
            'Includes Community Rewards and Member Airdrop 37%, Community Presale 10%, and Community Ecosystem and Expansion 20%, aimed at fostering community engagement, growth, and sustainability.'
    }
];

export default function Tokenonmics() {
    return (
        <section className="lg:bg-tertiary">
            <div className="mx-auto max-w-screen-xl py-10 lg:py-[130px]">
                <div className="block p-14 lg:hidden">
                    <div className="text-center">
                        <h2
                            className="font-changa text-5xl lg:text-[40px]"
                            style={{ lineHeight: '34px' }}
                        >
                            Tokenonmics:
                        </h2>
                        <p
                            className="mt-7 text-[17px]"
                            style={{ lineHeight: '27px' }}
                        >
                            LifeCoin, adhering to the ERC-20 standard, is
                            represented by the symbol $MLife. The total supply
                            of LifeCoin is capped at 5 billion tokens, with 2
                            billion tokens available at launch. The remaining 3
                            billion tokens are reserved for the operation of the
                            tokenized portfolio, ensuring the sustainability and
                            growth of the ManageLife ecosystem.
                        </p>
                    </div>
                </div>
                <div className="items-center justify-between gap-8 lg:flex">
                    <div className="lg:h-[368px] lg:w-[496px]">
                        <Image
                            src="/images/landing/chart.png"
                            width={1200}
                            height={1000}
                            className="hidden h-full w-full lg:block"
                            alt=""
                        />
                        <Image
                            src="/images/landing/mobile-chart.png"
                            width={1200}
                            height={1000}
                            className="block h-full w-full lg:hidden"
                            alt=""
                        />
                    </div>
                    <div className="grid flex-1 gap-5 p-10 lg:grid-cols-2 lg:p-0">
                        {allocationData.map((data, index) => (
                            <AllocationCard key={index} {...data} />
                        ))}
                    </div>
                </div>

                <div className="hidden pt-10 lg:block lg:pt-[170px]">
                    <div className="items-center justify-between gap-14 p-4 text-primary lg:flex lg:p-0">
                        <h2
                            className="font-bebas text-5xl lg:text-[75px]"
                            style={{ lineHeight: '75px' }}
                        >
                            Tokenonmics:
                        </h2>
                        <p
                            className="text-justify text-[17px]"
                            style={{ lineHeight: '27px' }}
                        >
                            LifeCoin, adhering to the ERC-20 standard, is
                            represented by the symbol $MLife. The total supply
                            of LifeCoin is capped at 5 billion tokens, with 2
                            billion tokens available at launch. The remaining 3
                            billion tokens are reserved for the operation of the
                            tokenized portfolio, ensuring the sustainability and
                            growth of the ManageLife ecosystem.
                        </p>
                    </div>
                </div>
            </div>
        </section>
    );
}

function AllocationCard({ number, title, description }: AllocationCardProps) {
    return (
        <div className="space-y-3 border border-[#D5D5D5] pb-6 pl-7 pr-3 pt-11 shadow-lg lg:text-primary lg:shadow-none">
            <p className="font-changa text-5xl lg:font-bebas">{number}</p>
            <div>
                <h4 className="text-lg font-bold">{title}</h4>
                <p className="text-sm">{description}</p>
            </div>
        </div>
    );
}
