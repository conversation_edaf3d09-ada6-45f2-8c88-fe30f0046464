// components/home/<USER>
'use client';

import { joinWaitlist } from '@/server/actions/waitlist/joinWaitlist';
import { useState, FormEvent, ChangeEvent } from 'react';

interface WaitlistProps {
    isOpen: boolean;
    onClose: () => void;
}

interface FormData {
    role: string;
    email: string;
    wallet?: string; // Optional
    dreamHome: string;
}

const roleOptions = [
    'homeowner',
    'renter',
    'buyer',
    'management',
    'make my voice',
    'others',
];

export default function Waitlist({ isOpen, onClose }: WaitlistProps) {
    const [formData, setFormData] = useState<FormData>({
        role: roleOptions[0],
        email: '',
        wallet: '',
        dreamHome: '',
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<
        'idle' | 'success' | 'error'
    >('idle');
    const [errorMessage, setErrorMessage] = useState('');

    const handleChange = (
        e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
    ) => {
        const { name, value } = e.target;
        setFormData((prev) => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsSubmitting(true);
        setSubmitStatus('idle');
        setErrorMessage('');

        if (!formData.email) {
            setErrorMessage('Email is required.');
            setIsSubmitting(false);
            setSubmitStatus('error');
            return;
        }

        try {
            const result = await joinWaitlist(formData);

            if (!result.success) {
                throw new Error(result.message || 'Something went wrong');
            }

            setSubmitStatus('success');
            setFormData({
                role: roleOptions[0],
                email: '',
                wallet: '',
                dreamHome: '',
            });
            // Optionally close modal after a delay or let user close it
            // setTimeout(() => {
            //   onClose();
            //   setSubmitStatus('idle'); // Reset for next open
            // }, 3000);

        } catch (error: any) {
            setErrorMessage(error.message || 'Failed to submit.');
            setSubmitStatus('error');
        } finally {
            setIsSubmitting(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4">
            <div className="relative w-full max-w-lg rounded-lg bg-gray-900 p-6 shadow-xl text-white">
                <button
                    onClick={() => {
                        onClose();
                        setSubmitStatus('idle'); // Reset status when manually closing
                        setErrorMessage('');
                    }}
                    className="absolute right-4 top-4 text-gray-400 hover:text-white"
                    aria-label="Close modal"
                >
                    ×
                </button>

                <h2 className="mb-6 text-center text-2xl font-semibold">
                    Join the Waitlist
                </h2>

                {submitStatus === 'success' ? (
                    <div className="text-center">
                        <p className="text-green-400 text-lg">Thank you for joining!</p>
                        <p className="mt-2 text-sm">We'll be in touch soon.</p>
                        <button
                            onClick={() => {
                                onClose();
                                setSubmitStatus('idle');
                            }}
                            className="mt-6 rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
                        >
                            Close
                        </button>
                    </div>
                ) : (
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <label htmlFor="role" className="block text-sm font-medium text-gray-300">
                                Choose your role:
                            </label>
                            <select
                                id="role"
                                name="role"
                                value={formData.role}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border-gray-600 bg-gray-800 p-2 text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                required
                            >
                                {roleOptions.map((option) => (
                                    <option key={option} value={option}>
                                        {option.charAt(0).toUpperCase() + option.slice(1)}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                                Email address:
                            </label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                value={formData.email}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border-gray-600 bg-gray-800 p-2 text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                required
                                placeholder="<EMAIL>"
                            />
                        </div>

                        <div>
                            <label htmlFor="wallet" className="block text-sm font-medium text-gray-300">
                                Wallet address (Optional):
                            </label>
                            <input
                                type="text"
                                id="wallet"
                                name="wallet"
                                value={formData.wallet}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border-gray-600 bg-gray-800 p-2 text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                placeholder="0x..."
                            />
                        </div>

                        <div>
                            <label htmlFor="dreamHome" className="block text-sm font-medium text-gray-300">
                                What is your dream home?
                            </label>
                            <textarea
                                id="dreamHome"
                                name="dreamHome"
                                value={formData.dreamHome}
                                onChange={handleChange}
                                rows={3}
                                className="mt-1 block w-full rounded-md border-gray-600 bg-gray-800 p-2 text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                placeholder="Describe your ideal living space..."
                                required
                            />
                        </div>

                        {submitStatus === 'error' && errorMessage && (
                            <p className="text-sm text-red-400">{errorMessage}</p>
                        )}

                        <div>
                            <button
                                type="submit"
                                disabled={isSubmitting}
                                className="w-full rounded-md bg-blue-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:opacity-50"
                            >
                                {isSubmitting ? 'Submitting...' : 'Submit'}
                            </button>
                        </div>
                    </form>
                )}
            </div>
        </div>
    );
}