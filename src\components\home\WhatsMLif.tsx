function WhatsMLif() {
    const cardData = [
        {
            number: '01.',
            text: 'Lower barriers to entry through programmable finance'
        },
        {
            number: '02.',
            text: 'Transparent and verifiable processes powered by blockchain'
        },
        {
            number: '03.',
            text: 'Capital efficiency and smarter pathways to ownership'
        },
        {
            number: '04.',
            text: 'A system built to complement, not dismantle, existing real estate infrastructure'
        }
    ];
    return (
        <>
            <div
                className="h-full w-full bg-cover bg-no-repeat"
                style={{
                    backgroundImage: "url('/images/landing/whats-mLif.png')"
                }}
            >
                <div className="mx-auto flex flex-col items-center justify-center px-4 pt-32 text-center text-black">
                    <h4 className="font-poppins flex w-full flex-col text-center text-[50px] font-[900] leading-[42px] tracking-[-0.04em] text-white md:text-[97px] md:leading-[94px] lg:font-[900]">
                        <span>
                            What sets <br className="block lg:hidden" /> MLife{' '}
                            <br className="block lg:hidden" />
                            Apart?
                        </span>
                    </h4>

                    <div className="py-9 text-center text-sm font-light text-white md:text-lg">
                        <p className="lg:w-[740px]">
                            Each year, over{' '}
                            <span className="font-bold">400,000 Families</span>{' '}
                            in the United States are unable to complete their
                            home purchases due to{' '}
                            <span className="font-bold">
                                High transaction costs
                            </span>
                            ,{' '}
                            <span className="font-bold">
                                Down payment shortfalls
                            </span>
                            , and{' '}
                            <span className="font-bold">Mortgage denials</span>.
                            Traditional systems have made homeownership
                            difficult, opaque, and exclusionary. MLife is here
                            to change that—not by removing essential
                            intermediaries, but by making the process more
                            efficient, transparent, and accessible.
                        </p>
                    </div>

                    <div className="my-10 grid grid-cols-1 gap-6 md:grid-cols-2">
                        {cardData.map((item, index) => (
                            <div
                                key={index}
                                className="flex items-center justify-center gap-4 rounded-2xl border border-white/20 bg-white/10 px-8 py-6 shadow-lg backdrop-blur-md md:px-14 md:py-8"
                            >
                                <h4 className="font-poppins border-r-2 border-white px-2 text-end text-[80px] font-[700] leading-[124px] tracking-[0%] text-white md:w-2/5">
                                    {item.number}
                                </h4>
                                <div className="text-start font-light leading-[20px] text-white md:w-3/5 md:text-lg">
                                    <p className="md:w-[280px] md:pe-[75px]">
                                        {item.text}
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>

                    <div className="pb-28 pt-10 text-center text-4xl font-semibold tracking-[-0.04em] text-white">
                        <p>
                            {/* We believe everyone deserves a real shot at owning
                            their future. */}
                        </p>
                    </div>
                </div>{' '}
            </div>
        </>
    );
}

export default WhatsMLif;
