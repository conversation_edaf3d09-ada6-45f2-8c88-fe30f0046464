'use client';

import { useState } from 'react';
import Waitlist from '../home/<USER>';
import SolidButton from '../button/SolidButton'; 

export default function MarketplaceComponent() {
    const [isWaitlistOpen, setWaitlistOpen] = useState(false);

    return (
        <>
            <section style={{
                    backgroundImage: "url('/images/mlife-footer-bg.png')"
                }} className="bg-gradient-to-t from-white via-white to-[#F5F5F5]">
                
                <div className="mx-auto flex min-h-screen max-w-screen-xl items-center justify-center px-6">
                    <div className="text-center rounded-2xl border border-white/30 bg-white/20 p-16 shadow-2xl backdrop-blur-lg transition-transform duration-300 ease-in-out hover:scale-[1.02] md:p-32">
                        <h1 className="font-poppins text-[40px] font-extrabold text-secondary sm:text-[65px] sm:tracking-[-0.04em]">
                            Coming Soon!
                        </h1>
                        <p className="mt-4 max-w-3xl text-lg text-gray-500">
                            The smart contract is under audit, once completed the
                            marketplace will be up and running.
                        </p>
                        <div className="mt-10 flex justify-center">
                            <SolidButton
                                onClick={() => setWaitlistOpen(true)}
                                className="mt-6 flex h-[50px] w-[210px] items-center justify-center rounded-full border border-[#05AAFF] bg-[#05AAFF] text-white transition-all duration-300 hover:shadow-[0_0_10px_white]"
                            >
                                <span className="text-sm md:text-base">
                                    Join the Waitlist
                                </span>
                            </SolidButton>
                        </div>
                    </div>
                </div>
            </section>
            <Waitlist
                isOpen={isWaitlistOpen}
                onClose={() => setWaitlistOpen(false)}
            />
        </>
    );
}
