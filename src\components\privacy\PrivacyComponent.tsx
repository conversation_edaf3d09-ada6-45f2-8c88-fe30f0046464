// 'use client';
// import Script from 'next/script';
// import { useEffect } from 'react';

// function PrivacyComponent() {

//     return (
//         <section>
//             <div className="mx-auto min-h-screen max-w-screen-xl px-0 pb-10 pt-6 sm:px-0 md:pt-[150px] md:text-justify">
//                 <div
//                     data-id="97661420-1d5a-47a6-a8a5-08f99697e944"
//                     {...{ name: 'termly-embed' as any }}
//                 ></div>

//                 <Script
//                     id="termly-jssdk"
//                     src="https://app.termly.io/embed-policy.min.js"
//                     strategy="afterInteractive"
//                 />
//             </div>
//         </section>
//     );
// }

// export default PrivacyComponent;

'use client';
import { useEffect } from 'react';

function PrivacyComponent() {
    useEffect(() => {
        const script = document.createElement('script');
        script.src = 'https://app.termly.io/embed-policy.min.js';
        script.async = true;
        script.id = 'termly-jssdk';
        document.body.appendChild(script);

        return () => {
            document.getElementById('termly-jssdk')?.remove();
        };
    }, []);

    return (
        <section>
            <div className="mx-auto min-h-screen max-w-screen-xl px-0 pb-10 pt-6 sm:px-0 md:pt-[150px] md:text-justify">
                <div
                    data-id="97661420-1d5a-47a6-a8a5-08f99697e944"
                    {...{ name: 'termly-embed' as any }}
                ></div>
            </div>
        </section>
    );
}

export default PrivacyComponent;
