'use client';

import Image from 'next/image';
import { FaLinkedin } from 'react-icons/fa';
import { FaXTwitter } from 'react-icons/fa6';

type TeamMember = {
    name: string;
    role: string;
    image: string;
    description: string;
};

const teamData: TeamMember[] = [
    {
        name: '<PERSON>',
        role: 'CEO, Founder',
        image: '/images/team/jake.png',
        description:
            "Harvard Finance turned degen. Bitcoin '16. Ex-Large Scale Bitcoin Miner. SpiritDAO Founding Member & Active Web3 Investor. Founder of RWA platform, ManageLife.io"
    },
    {
        name: '<PERSON>',
        role: 'CFO',
        image: '/images/team/jordan.png',
        description:
            'A seasoned full-stack engineer  specializing in Web3 and blockchain solutions. With experience at small and large companies alike, he excels in building scalable, user-centric applications that bridge traditional finance with emerging technologies.'
    },
    {
        name: '<PERSON>',
        role: 'COO',
        image: '/images/team/wang.png',
        description:
            'A decade of experience in the commercial real estate of over $550 million capitalization. Specialized in the cash-flow portfolio investment and business development. Managing the real estate portfolio and conducting investment pipelines.'
    },
    {
        name: '<PERSON>',
        role: 'Business Relations',
        image: '/images/team/Lesley.png',
        description:
            ''
    },
    {
        name: '<PERSON><PERSON><PERSON>duk',
        role: 'Strategic Blockchain Associate',
        image: '/images/team/Kateryna.png',
        description:
            ''
    },    
    {
        name: 'Lisa Smith',
        role: 'Accounting Lead',
        image: '/images/team/lisa.png',
        description:
            ''
    },   
    {
        name: 'Shamir Shakher',
        role: 'Principle Engineer',
        image: '/images/team/shamir.png',
        description:
            'Full-stack craftsman turned Web3 architect. Founder of w3ultra, where digital meets decentralized. Specializes in building apps, smart contracts, and scalable platforms that empower the future of web3. Passionate about turning complex ideas into seamless user experiences. Bridging Web2 logic with Web3 freedom.'
    }
];
function TeamCard() {
    return (
        <div className="mx-auto max-w-screen-xl">
            <div className="grid grid-cols-1 md:grid-cols-2 md:gap-10 lg:grid-cols-3">
                {teamData?.map((member: TeamMember, index: number) => (
                    <div
                        key={index}
                        className="m-8 border shadow-xl md:w-[400px] md:p-0 md:shadow-none"
                    >
                        <Image
                            src={member.image}
                            width={500}
                            height={500}
                            alt="Adam"
                            className="m-5 mx-auto h-[300px] w-[300px] object-cover md:m-10"
                        />
                        {/* <h4 className="font-poppins pt-3 text-center text-3xl md:pt-0 md:text-5xl md:font-[700] md:tracking-[-0.05em]">
                            {member.name}
                        </h4>
                        <h6 className="text-center font-sans text-xl md:text-2xl">
                            {member.role}
                        </h6> */}
                        <div className="flex items-center justify-between px-6">
                            <div>
                                <h4 className="pt-3 text-start text-3xl font-bold">
                                    {member.name}
                                </h4>
                                <h6 className="text-start font-sans text-xl">
                                    {member.role}
                                </h6>
                            </div>
                            <div className="flex gap-2">
                                <FaLinkedin size={20} />{' '}
                                <FaXTwitter size={20} />
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}

export default TeamCard;
