'use client';
import { useEffect } from 'react';

function TermsComponent() {
    useEffect(() => {
        const script = document.createElement('script');
        script.src = 'https://app.termly.io/embed-policy.min.js';
        script.async = true;
        script.id = 'termly-jssdk';
        document.body.appendChild(script);

        return () => {
            document.getElementById('termly-jssdk')?.remove();
        };
    }, []);

    return (
        <section>
            <div className="mx-auto min-h-screen max-w-screen-xl px-0 pb-10 pt-6 sm:px-0 md:pt-[150px] md:text-justify">
                <div
                    data-id="12bbd42e-d6c3-4484-ac3a-0dd0e258c0b8"
                    {...{ name: 'termly-embed' as any }}
                ></div>
            </div>
        </section>
    );
}

export default TermsComponent;
