import mongoose, { Document, Schema } from 'mongoose';

// Define interface for the document
interface IWalletConnection extends Document {
  address: string;
  connectedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Create schema
const WalletConnectionSchema = new Schema<IWalletConnection>({
  address: {
    type: String,
    required: true,
    trim: true,
    index: true,
    unique: true
  },
  connectedAt: {
    type: Date,
    required: true
  }
}, {
  timestamps: true // Adds createdAt and updatedAt fields automatically
});

// Create and export model
const WalletConnection = mongoose.models.WalletConnection || mongoose.model<IWalletConnection>('WalletConnection', WalletConnectionSchema);

export default WalletConnection;