// /* eslint-disable @typescript-eslint/no-explicit-any */
// "use server";

// import Product from "@/server/model/product/productModel";
// import connectMongo from "@/server/utils/connection";
// import { revalidatePath } from "next/cache";
// import slugify from "slugify";

// export const addProduct = async (payload) => {
//   try {
//     await connectMongo();

//     const baseSlug = slugify(payload?.title, {
//       replacement: "-",
//       remove: /[^a-zA-Z0-9\s]/g,
//       lower: true,
//       trim: true,
//     });

//     let slug = baseSlug;
//     let suffix = 1;

//     // Check for existing slugs
//     while (await Product.exists({ slug })) {
//       slug = `${baseSlug}-${suffix}`;
//       suffix++;
//     }

//     payload.slug = slug;

//     const newProduct = await Product.create(payload);

//     if (!newProduct?._id) {
//       throw new Error("Failed to Create Product!");
//     }

//     revalidatePath("/dashboard/products");

//     return {
//       success: true,
//       message: "Product created successfully!",
//     };
//   } catch (err: any) {
//     if (err.code === 11000) {
//       return {
//         success: false,
//         message: `Product with the name "${payload?.title}" already exists. Please use a unique product name.`,
//       };
//     } else {
//       return {
//         success: false,
//         message: err.message,
//       };
//     }
//   }
// };
