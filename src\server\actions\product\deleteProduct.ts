// /* eslint-disable @typescript-eslint/no-explicit-any */
// "use server";

// import Product from "@/server/model/product/productModel";
// import connectMongo from "@/server/utils/connection";
// import { revalidatePath } from "next/cache";

// export const deleteProduct = async (id: string) => {
//   try {
//     await connectMongo();

//     const deletedInfo = await Product.deleteOne({ _id: id });

//     if (deletedInfo?.deletedCount > 0) {
//       revalidatePath("/dashboard/products");
//       return { success: true, message: "Product deleted successfully!" };
//     } else {
//       return { success: false, message: "Product not deleted." };
//     }
//   } catch (err: any) {
//     return {
//       success: false,
//       message: err.message,
//     };
//   }
// };
