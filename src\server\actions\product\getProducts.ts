/* eslint-disable @typescript-eslint/no-explicit-any */
'use server';

import { NftModel } from '@/server/model/product/productModel';
import { TNft } from '@/server/model/product/productType';
import connectMongo from '@/server/utils/connection';

// lib/getAllProducts.ts

export async function getAllProducts({ type }: any) {
    await connectMongo();

    const query = { isMinted: true, isListing: true };

    const products = await NftModel.find(query)
        .populate('property')
        .lean()
        .exec();

    return products as unknown as TNft[];
}

// export const getProductsCount = async () => {
//   try {
//     await connectMongo();

//     const products = await Product.countDocuments({});

//     return {
//       success: true,
//       message: "Products fetched successfully",
//       data: products,
//     };
//   } catch (err: any) {
//     return {
//       success: false,
//       message: err.message,
//     };
//   }
// };
// export const getProductsForProductPage = async (
//   page: number = 1,
//   limit: number = 10,
//   categorySlag?: string // Fixed parameter syntax
// ) => {
//   try {
//     await connectMongo();

//     const skip = (page - 1) * limit;
//     const filter: any = {};

//     if (categorySlag) {
//       filter.category_id = categorySlag;
//     }

//     const total = await Product.countDocuments(filter);
//     const products = await Product.find(filter)
//       .sort("-createdAt")
//       .skip(skip)
//       .limit(limit)
//       .lean(); // Optimized query performance

//     return {
//       success: true,
//       message: "Products fetched successfully",
//       data: products,
//       pagination: {
//         total,
//         page,
//         limit,
//         totalPages: Math.ceil(total / limit),
//       },
//     };
//   } catch (err: any) {
//     return {
//       success: false,
//       message: err.message,
//     };
//   }
// };

// export const getFeaturedProducts = async () => {
//   try {
//     await connectMongo();

//     const featuredProducts = await Product.find({ featured: "1" }).lean();

//     return {
//       success: true,
//       message: "Featured products fetched successfully",
//       data: featuredProducts,
//     };
//   } catch (err: any) {
//     return {
//       success: false,
//       message: err.message,
//     };
//   }
// };

// adjust the path
type SearchProductsResult =
    | {
          success: true;
          message: string;
          data: TNft[];
      }
    | {
          success: false;
          message: string;
          data?: undefined;
      };
export const searchProducts = async (
    query: string
): Promise<SearchProductsResult> => {
    try {
        await connectMongo();
        // if (!query) {
        //     const query = { isMinted: true, isListing: true };

        //     const products = await NftModel.find(query)
        //         .populate('property')
        //         .lean()
        //         .exec();

        //     return {
        //         success: true,
        //         message: 'Products search completed successfully',
        //         data: products as unknown as TNft[]
        //     };
        // }
        const products = await NftModel.find({
            $or: [
                { nftName: { $regex: query, $options: 'i' } }
                // {
                //     'property.propertyAddressOne': {
                //         $regex: query,
                //         $options: 'i'
                //     }
                // },
                // { 'property.city': { $regex: query, $options: 'i' } },
                // { 'property.state': { $regex: query, $options: 'i' } }
            ]
        })
            .populate('property')
            .lean()
            .exec();

        return {
            success: true,
            message: 'Products search completed successfully',
            data: products as unknown as TNft[]
        };
    } catch (err: any) {
        return {
            success: false,
            message: err.message
        };
    }
};

export const getProductById = async (id: string) => {
    try {
        await connectMongo();

        const product = await NftModel.findById(id)
            .populate('property')
            .lean()
            .exec();

        if (!product) {
            return {
                success: false,
                message: 'Product not found'
            };
        }

        return {
            success: true,
            message: 'Product fetched successfully',
            data: product
        };
    } catch (err: any) {
        return {
            success: false,
            message: err.message
        };
    }
};
