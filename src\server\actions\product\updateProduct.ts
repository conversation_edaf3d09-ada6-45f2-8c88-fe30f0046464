// /* eslint-disable @typescript-eslint/no-explicit-any */
// "use server";

// import Product from "@/server/model/product/productModel";
// import connectMongo from "@/server/utils/connection";
// import { revalidatePath } from "next/cache";

// export const updateProduct = async (id: string, payload: any) => {
//   try {
//     await connectMongo();

//     const updateData = { ...payload };

//     if (payload?.title) {
//       updateData.slug = payload?.title
//         ?.trim()
//         .toLowerCase()
//         .replace(/[^a-zA-Z0-9 ]/g, "")
//         .replace(/\s+/g, " ")
//         .replaceAll(" ", "-");
//     }

//     const eventData = await Product.findOneAndUpdate({ _id: id }, updateData, {
//       new: true,
//     });

//     if (!eventData?._id) {
//       throw new Error("Failed to Update Product!");
//     }

//     revalidatePath("/dashboard/products");

//     return {
//       success: true,
//       message: "Product updated successfully!",
//     };
//   } catch (err: any) {
//     if (err.code === 11000) {
//       return {
//         success: false,
//         message: `Product with the name "${payload?.data?.productName}" already exists. Please use a unique product name.`,
//       };
//     } else {
//       return {
//         success: false,
//         message: err.message,
//       };
//     }
//   }
// };

// export const updateFeaturedProduct = async (
//   id: string,
//   featured: "0" | "1"
// ) => {
//   try {
//     await connectMongo();

//     const updatedProduct = await Product.findOneAndUpdate(
//       { _id: id },
//       { featured },
//       { new: true }
//     );

//     if (!updatedProduct?._id) {
//       throw new Error("Failed to update featured status.");
//     }

//     revalidatePath("/dashboard/products");
//     return {
//       success: true,
//       message: `Product ${featured === "1" ? "added to" : "removed from"} featured list successfully.`,
//     };
//   } catch (err: any) {
//     return {
//       success: false,
//       message: err.message || "Something went wrong.",
//     };
//   }
// };
