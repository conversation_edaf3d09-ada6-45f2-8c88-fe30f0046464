// "use server";

// import User from "@/server/model/user/userModel";
// import connectMongo from "@/server/utils/connection";

// import jwt from "jsonwebtoken";
// import { cookies } from "next/headers";

// // login action
// export default async function loginAction(data: {
//   email: string;
//   password: string;
// }) {
//   await connectMongo();

//   // Check If User Is Exist
//   const user = await User.findOne({
//     email: data.email,
//     password: data.password,
//   });

//   if (!user) {
//     return {
//       success: false,
//       error:
//         "Sorry, this email doesn't exist in our records. Please double-check your entry or sign up if you're new here.",
//     };
//   }

//   // We omit the password using destructuring
//   // eslint-disable-next-line @typescript-eslint/no-unused-vars
//   const { password, ...cleanUserObject } = user.toObject();

//   // Create Token & Set Token To Cookies
//   const token = jwt.sign(
//     { user: cleanUserObject },
//     process.env.TOKEN_SECRET as string,
//     { expiresIn: "30d" }
//   );

//   const result = (await cookies()).set("gsk_token", token, {
//     maxAge: 2592000,
//   });

//   if (result) {
//     return {
//       success: true,
//       message: "Login successfully",
//     };
//   }
// }

// // Get all user database
// export const GetAllUsers = async () => {
//   try {
//     await connectMongo();
//     const AllUsers = await User.find().lean();
//     return AllUsers;
//   } catch (error) {
//     console.error("Error getting User:", error);
//     throw new Error("Error getting Users");
//   }
// };

// export async function updatePasswordAction(data: {
//   email: string;
//   password: string;
//   newPassword: string;
// }) {
//   await connectMongo();

//   // Check If User Is Exist
//   const user = await User.findOne({
//     email: data.email,
//     password: data.password,
//   });

//   if (!user) {
//     return {
//       success: false,
//       error:
//         "Sorry, this email doesn't exist in our records. Please double-check your entry or sign up if you're new here.",
//     };
//   }

//   const result = await User.updateOne(
//     { email: data.email },
//     { $set: { password: data.newPassword } }
//   );

//   if (result) {
//     return {
//       success: true,
//       message: "Password updated successfully",
//     };
//   }
// }
