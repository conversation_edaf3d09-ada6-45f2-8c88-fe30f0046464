'use server';

interface WaitlistFormData {
    role: string;
    email: string;
    wallet?: string;
    dreamHome: string;
}

// Basic email validation regex
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

export async function joinWaitlist(formData: WaitlistFormData) {
    if (!formData.email || !emailRegex.test(formData.email)) {
        return {
            success: false,
            message: 'A valid email is required.'
        };
    }

    try {
        // In a real application, you would add the data to your database.
        // For example: await db.waitlist.create({ data: formData });
        console.log('New waitlist submission:', formData);

        return {
            success: true,
            message: "You've been added to the waitlist!"
        };
    } catch (error) {
        console.error('Error adding to waitlist:', error);
        return {
            success: false,
            message: 'Something went wrong. Please try again later.'
        };
    }
} 