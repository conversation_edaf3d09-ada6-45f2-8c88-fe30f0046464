// server/models/WaitlistEntry.ts
import mongoose, { Schema, Document, Model } from 'mongoose';

// Define the interface for the document
export interface IWaitlistEntry extends Document {
    role: string;
    email: string; // Will be stored in lowercase
    wallet?: string;
    dreamHome: string;
    createdAt: Date;
}

// Define the Mongoose schema
const WaitlistEntrySchema: Schema<IWaitlistEntry> = new Schema({
    role: {
        type: String,
        required: [true, 'Role is required.'],
        trim: true,
    },
    email: {
        type: String,
        required: [true, 'Email is required.'],
        unique: true, // Ensures email is unique in the collection
        lowercase: true, // Automatically converts email to lowercase
        trim: true,
        match: [/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please enter a valid email address.'],
    },
    wallet: {
        type: String,
        trim: true,
        default: '', // Default to empty string if not provided
    },
    dreamHome: {
        type: String,
        required: [true, 'Dream home description is required.'],
        trim: true,
    },
    createdAt: {
        type: Date,
        default: Date.now,
    },
});

// Create and export the Mongoose model
// The mongoose.models.WaitlistEntry || ... part prevents overwriting the model if it's already compiled
// which can happen in serverless environments or with Next.js hot reloading.
const WaitlistEntryModel: Model<IWaitlistEntry> = 
    mongoose.models.WaitlistEntry || mongoose.model<IWaitlistEntry>('WaitlistEntry', WaitlistEntrySchema, 'waitlistEntries');
    // The third argument 'waitlistEntries' explicitly sets the collection name.
    // If omitted, Mongoose would pluralize 'WaitlistEntry' to 'waitlistentries'.

export default WaitlistEntryModel;