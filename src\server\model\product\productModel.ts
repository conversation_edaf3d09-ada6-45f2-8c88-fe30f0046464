import mongoose, { Document, model, models, Schema } from 'mongoose';
import { Log, Property, TNft } from './productType';

interface IProperty extends Omit<Property, '_id'>, Document {}
interface ILog extends Log, Document {}
interface INft extends Omit<TNft, 'property' | '_id'>, Document {
    _id: mongoose.Types.ObjectId;
    property: mongoose.Types.ObjectId | IProperty;
}

const PropertySchema = new Schema<IProperty>(
    {
        userId: { type: Schema.Types.ObjectId, ref: 'User' },
        propertyId: String,
        propertyName: String,
        propertyType: String,
        propertyAddressOne: String,
        propertyAddressTwo: String,
        country: String,
        city: String,
        state: String,
        zipCode: String
    },
    { timestamps: true }
);

const LogSchema = new Schema<ILog>(
    {
        date: Date,
        transaction_type: String,
        from: String,
        transaction_hash: String,
        to: String,
        block_hash: String,
        amount: String
    },
    { _id: false }
);

const NftSchema = new Schema<INft>(
    {
        isDeActivated: Boolean,
        isMinted: Boolean,
        isListing: Boolean,
        isVerified: Boolean,
        otherImages: [String],
        owner: { type: Schema.Types.ObjectId, ref: 'User' },
        property: { type: Schema.Types.ObjectId, ref: 'Property' },
        bids: Array,
        logs: [LogSchema],
        tokenId: Number,
        abstractCode: String,
        animationURL: String,
        annualAppreciationRate: Number,
        annualNetCashflow: Number,
        appraisedValue: Number,
        bathrooms: Number,
        bedrooms: Number,
        buildingSize: Number,
        cashOnCash: Number,
        ccrDeclaration: String,
        cooling: String,
        currentRent: Number,
        daoContract: String,
        description: String,
        doingBusinessAs: String,
        geoID: String,
        heating: String,
        image: String,
        insurancePremium: Number,
        legalDescription: String,
        lotSize: Number,
        maintenance: Number,
        marketAveragePrice: Number,
        marketPrice: Number,
        memContract: String,
        monthlyExpense: Number,
        monthlyRevenue: Number,
        neighborhood: String,
        nftName: String,
        operatingAgrmt: String,
        ownerName: String,
        price: Number,
        propertyTax: Number,
        squareFeet: Number,
        stateCode: String,
        story: Number,
        structure: String,
        targetIrr: Number,
        targetMarketPriceInThreeYears: Number,
        taxJurisdictions: String,
        tokenRate: Number,
        valuation: Number,
        yearBuilt: Number,
        youtubeURL: String,
        hoa: String,
        standout: String,
        unit: String,
        garageSpace: String,
        schoolRating: String,
        swimmingPool: String,
        updatedAt: Date
    },
    { timestamps: true }
);

export const PropertyModel =
    models.Property || model<IProperty>('Property', PropertySchema);
export const NftModel = models.Nft || model<INft>('Nft', NftSchema);
