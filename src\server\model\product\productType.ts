// types/product.ts
export interface Log {
    date: string;
    transaction_type: string;
    from: string;
    transaction_hash: string;
    to: string;
    block_hash: string;
    amount?: string;
}

export interface Property {
    _id: string;
    userId?: string;
    propertyId: string;
    propertyName: string;
    propertyType: string;
    propertyAddressOne: string;
    propertyAddressTwo: string;
    country: string;
    city: string;
    state: string;
    zipCode: string;
}

export interface TNft {
    _id?: string;
    isDeActivated: boolean;
    isMinted: boolean;
    isListing: boolean;
    isVerified: boolean;
    otherImages: string[];
    owner?: string;
    property: Property;
    bids: any[];
    logs: Log[];
    tokenId: number;
    abstractCode: string;
    animationURL: string;
    annualAppreciationRate: number;
    annualNetCashflow: number;
    appraisedValue: number | null;
    bathrooms: number;
    bedrooms: number;
    buildingSize: number;
    cashOnCash: number;
    ccrDeclaration: string;
    cooling: string;
    currentRent: number;
    daoContract: string;
    description: string;
    doingBusinessAs: string;
    geoID: string;
    heating: string;
    image: string;
    insurancePremium: number;
    legalDescription: string;
    lotSize: number;
    maintenance: number;
    marketAveragePrice: number | null;
    marketPrice: number;
    memContract: string;
    monthlyExpense: number;
    monthlyRevenue: number;
    neighborhood: string;
    nftName: string;
    operatingAgrmt: string;
    ownerName: string;
    price: number;
    propertyTax: number;
    squareFeet: number;
    stateCode: string;
    story: number;
    structure: string;
    targetIrr: number | null;
    targetMarketPriceInThreeYears: number | null;
    taxJurisdictions: string;
    tokenRate: number;
    valuation: number;
    yearBuilt: number;
    youtubeURL: string;
    hoa: string;
    standout: string;
    unit: string;
    garageSpace: string;
    schoolRating: string;
    swimmingPool: string;
    updatedAt: string;
    createdBy: string;
}
