// import mongoose, { Model, Schema } from "mongoose";
// import { IUser } from "./userType";

// const userSchema: Schema<IUser> = new Schema<IUser>(
//   {
//     email: String,
//     password: String,
//   },
//   { timestamps: true }
// );

// export type UserModel = Model<IUser>;

// const User =
//   (mongoose.models.User as UserModel) ||
//   mongoose.model<IUser, UserModel>("User", userSchema);

// export default User;
