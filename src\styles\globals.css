@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Web3Modal 样式重写 */
.w3m-overlay {
  z-index: 1000 !important;
}

.w3m-modal {
  z-index: 1001 !important;
}

/* 确保 Web3Modal 按钮样式优先级高 */
.w3m-button {
  z-index: 999 !important;
  pointer-events: auto !important;
}

.w3m-account-button {
  z-index: 999 !important;
  pointer-events: auto !important;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    overflow: visible;

    /* user-select: none; */
}
html {
    scroll-behavior: smooth;
}

:root {
    --color-primary: #ffffff;
    --color-secondary: #1e1b39; /* Dark Blue */
    --color-tertiary: #000000;
    --color-accent_blue: #0088ff;
    --color-navy_blue: #000929;
    --color-gray_light: #e9e7e7;
}
.bg-roadmap-mobile {
    background-image: url('/images/landing/mobile-roadmap-bg-image.png');
}
.bg-solutions-mobile {
    background-image: url('/images/landing/mobile-solutions-image.png');
}
.loader {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #000;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 0.6s linear infinite;
    display: inline-block;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
/* globals.css */
body.modal-open {
    overflow: hidden !important;
    position: fixed;
    width: 100%;
}

.scrollbar-hidden::-webkit-scrollbar {
    display: none;
}

.scrollbar-hidden {
    -ms-overflow-style: none;
    scrollbar-width: none;
    overflow: auto;
}
iframe {
    background-color: white !important;
}
/* loader animation */
.loader {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #333;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 0.8s linear infinite;
    display: inline-block;
    margin-right: 8px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
/* Remove spinner arrows from number input */
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type='number'] {
    -moz-appearance: textfield; /* Firefox */
}


.video-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

/* 视频元素样式 */
.background-video {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    min-width: 100%;
    min-height: 100%;
    z-index: 0;
}

/* 文字容器 */
.text-overlay {
    position: relative;
    z-index: 1;
}

/* 打字机动画 */
.typewriter {
    overflow: hidden;
    border-right: 2px solid white; /* 光标 */
    white-space: nowrap;
    animation: 
        typing 3.5s steps(40, end),
        blink-caret 0.75s step-end infinite,
        hide-caret 0.1s 3.5s forwards; /* 新增隐藏动画 */
}

/* 光标闪烁动画 */
@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: white; }
}

/* 新增隐藏光标动画 */
@keyframes hide-caret {
    to {
        border-right-color: transparent;
    }
}

/* 副标题动画 */
.subtitle-typewriter {
    overflow: hidden;
    white-space: nowrap;
    animation: 
        typing 2s steps(30, end) 3.5s; /* 延迟3.5秒 */
    animation-fill-mode: both;
}

@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}

@keyframes blink-caret {
    from, to { border-color: transparent }
    50% { border-color: white; }
}

/* 主标题多行适配 */
.main-heading {
    display: inline-block;
    white-space: nowrap;
}

.main-heading br {
    display: none; /* 暂时隐藏换行 */
}

.main-heading::after {
    content: "";
    animation: second-line 3s steps(40, end) 3.5s;
    animation-fill-mode: both;
}

@keyframes second-line {
    from { content: "" }
    to { content: "Reinvented"; }
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 40s linear infinite;
  width: max-content;
}

/* 鼠标悬停暂停动画 */
.logos-container:hover .animate-scroll {
  animation-play-state: paused;
}

/* 3D透视效果 */
.logos-track {
  transform-style: preserve-3d;
}

.logo-item {
  transform: perspective(1000px) translateZ(0);
  transition: transform 0.3s;
}

.logo-item:hover {
  transform: perspective(1000px) translateZ(20px);
}


/* 全局样式 */
.pdf-container {
  background: white; /* 将黑边改为白色 */
  padding: 1rem;
}

.pdf-page {
  margin: 0 auto; /* 水平居中 */
}

/* 移除react-pdf默认边框 */
.react-pdf__Page {
  box-shadow: none !important;
}


.nav-link {
  transition: all 0.3s ease;
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 20px;
  left: 0;
  width: 0;
  height: 2px;
  background: #4f46e5;
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.active-link {
  color: #4f46e5;
  transform: scale(1.1);
}

.hover-glow {
  position: relative;
  overflow: hidden;
}

.hover-glow::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(99,102,241,0.2) 0%, rgba(0,0,0,0) 70%);
  transform: translate(25%, 25%);
  opacity: 0;
  transition: opacity 0.3s;
}

.hover-glow:hover::before {
  opacity: 1;
}


