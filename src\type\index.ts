export type OfferFormInputs = {
    buyerName: string;
    buyerEmail: string;
    sellerAgentPhone: string;
    sellerAgentEmail: string;
    sellerAgentName: string;
    MLSNumber: string;
    streetAddress: string;
    unitNumber: string;
    zipCode: string;
    state: string;
    offerPrice: string;
    depositPercentage: string;
    depositAmount: string;
    financing: string;
    loanAmount: string;
    preQualified: string;
    additionalFinancing: string;
    amount: string;
    inspection: string;
    financeContingency: string;
    appraisalContingency: string;
    closingDate: string;
    homeWarranty: string;
    homeWarrantyAmount: string;
    closingCost: string;
    closingCostAmount: string;
    preferredLender: string;
    titleCompany: string;
    escrowCompany: string;
};
// types.ts or inside your component file
export type TListingFormInputs = {
    MLSNumber: string;
    streetAddress: string;
    unitNumber: string;
    zipCode: string;
    state: string;
    amount: number; // Price
    currency: string;
    rentAmount: number; // Rent price
    rentDuration: string; // Rent duration (Per month/year)
    sellAsNFT: boolean;
    acceptCrypto: boolean;
    parentProperty: string;
    status: string;
    bedrooms: number;
    bathrooms: number;
    floors: number;
    garages: number;
    yearBuilt: string;
    homeArea: number;
    lotDimensions: string;
    schoolRatings: string; // maybe a date for now, adjust later
};
