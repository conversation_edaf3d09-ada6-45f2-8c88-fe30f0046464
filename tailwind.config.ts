import type { Config } from 'tailwindcss';

const config: Config = {
    content: [
        './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
        './src/components/**/*.{js,ts,jsx,tsx,mdx}',
        './src/app/**/*.{js,ts,jsx,tsx,mdx}'
    ],
    theme: {
        extend: {
            fontFamily: {
                sans: `var(--font-roboto), sans-serif`,
                bebas: `var(--font-bebas-neue), sans-serif`,
                changa: `var(--font-changa-one), cursive`,
                changaRegular: 'var(--font-changaRegular), sans-serif',
                poppins: `var(--font-poppins), sans-serif`
            },
            screens: {
                sm: '576px',
                md: '768px',
                lg: '992px',
                xl: '1280px',
                '2xl': '1320px',
                '3xl': '1440px',
                '4xl': '1569px'
            },
            colors: {
                primary: '#FFFFFF',
                secondary: '#1E1B39', // Dark Blue
                tertiary: '#000000',
                accent_blue: '#0088FF',
                navy_blue: '#000929',
                gray_light: '#E9E7E7'
            }
        }
    }
};

export default config;
